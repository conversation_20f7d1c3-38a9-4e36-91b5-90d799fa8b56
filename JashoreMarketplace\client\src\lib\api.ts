import { apiRequest } from "./queryClient";

export interface CreateProductData {
  title: string;
  description: string;
  price: number;
  condition: string;
  categoryId: number;
  locationId: number;
  contactPhone?: string;
  images: File[];
}

export interface CreateMessageData {
  content?: string;
  image?: File;
}

export interface CreateBlogPostData {
  title: string;
  content: string;
  excerpt?: string;
  featuredImage?: File;
}

export const api = {
  // Products
  async createProduct(data: CreateProductData) {
    const formData = new FormData();
    Object.entries(data).forEach(([key, value]) => {
      if (key === 'images' && Array.isArray(value)) {
        value.forEach(file => formData.append('images', file));
      } else if (value !== undefined) {
        formData.append(key, value.toString());
      }
    });

    return await apiRequest('POST', '/api/products', formData, {
      headers: undefined, // Let browser set Content-Type for FormData
    });
  },

  async updateProduct(id: string, data: Partial<CreateProductData>) {
    return await apiRequest('PUT', `/api/products/${id}`, data);
  },

  async deleteProduct(id: string) {
    return await apiRequest('DELETE', `/api/products/${id}`);
  },

  // Conversations
  async createConversation(productId: string, sellerId: string) {
    return await apiRequest('POST', '/api/conversations', {
      productId,
      sellerId,
    });
  },

  // Messages
  async sendMessage(conversationId: string, data: CreateMessageData) {
    const formData = new FormData();
    
    if (data.content) {
      formData.append('content', data.content);
    }
    
    if (data.image) {
      formData.append('image', data.image);
    }

    return await apiRequest('POST', `/api/conversations/${conversationId}/messages`, formData, {
      headers: undefined,
    });
  },

  // Blog
  async createBlogPost(data: CreateBlogPostData) {
    const formData = new FormData();
    Object.entries(data).forEach(([key, value]) => {
      if (key === 'featuredImage' && value instanceof File) {
        formData.append('featuredImage', value);
      } else if (value !== undefined && typeof value === 'string') {
        formData.append(key, value);
      }
    });

    return await apiRequest('POST', '/api/blog', formData, {
      headers: undefined,
    });
  },

  async updateBlogPost(id: string, data: Partial<CreateBlogPostData>) {
    return await apiRequest('PUT', `/api/blog/${id}`, data);
  },

  async publishBlogPost(id: string) {
    return await apiRequest('POST', `/api/blog/${id}/publish`);
  },

  // Favorites
  async addToFavorites(productId: string) {
    return await apiRequest('POST', `/api/favorites/${productId}`);
  },

  async removeFromFavorites(productId: string) {
    return await apiRequest('DELETE', `/api/favorites/${productId}`);
  },

  // Admin
  async verifyUser(userId: string) {
    return await apiRequest('POST', `/api/admin/users/${userId}/verify`);
  },

  async makeAdmin(userId: string) {
    return await apiRequest('POST', `/api/admin/users/${userId}/make-admin`);
  },
};
