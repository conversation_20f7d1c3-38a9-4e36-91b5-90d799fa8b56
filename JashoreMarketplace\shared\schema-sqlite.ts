import {
  sqliteTable,
  text,
  integer,
  real,
  index,
} from "drizzle-orm/sqlite-core";
import { relations } from "drizzle-orm";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// Session storage table
export const sessions = sqliteTable(
  "sessions",
  {
    sid: text("sid").primaryKey(),
    sess: text("sess").notNull(), // JSON as text in SQLite
    expire: integer("expire", { mode: 'timestamp' }).notNull(),
  },
  (table) => [index("IDX_session_expire").on(table.expire)],
);

// User storage table
export const users = sqliteTable("users", {
  id: text("id").primaryKey().notNull(),
  email: text("email").unique(),
  firstName: text("first_name"),
  lastName: text("last_name"),
  profileImageUrl: text("profile_image_url"),
  phone: text("phone"),
  password: text("password"), // For local authentication
  isVerified: integer("is_verified", { mode: 'boolean' }).default(false),
  isAdmin: integer("is_admin", { mode: 'boolean' }).default(false),
  role: text("role").default("user"), // user, admin, super_admin
  lastSeen: integer("last_seen", { mode: 'timestamp' }),
  createdAt: integer("created_at", { mode: 'timestamp' }),
  updatedAt: integer("updated_at", { mode: 'timestamp' }),
});

export const categories = sqliteTable("categories", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  name: text("name").notNull(),
  nameBn: text("name_bn").notNull(),
  icon: text("icon").notNull(),
  slug: text("slug").notNull().unique(),
  parentId: integer("parent_id"),
  isActive: integer("is_active", { mode: 'boolean' }).default(true),
  createdAt: integer("created_at", { mode: 'timestamp' }),
});

export const locations = sqliteTable("locations", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  district: text("district").notNull(),
  districtBn: text("district_bn").notNull(),
  area: text("area").notNull(),
  areaBn: text("area_bn").notNull(),
  isActive: integer("is_active", { mode: 'boolean' }).default(true),
  createdAt: integer("created_at", { mode: 'timestamp' }),
});

export const products = sqliteTable("products", {
  id: text("id").primaryKey(),
  title: text("title").notNull(),
  description: text("description").notNull(),
  price: real("price").notNull(),
  condition: text("condition").notNull(), // 'new', 'used'
  categoryId: integer("category_id").notNull(),
  locationId: integer("location_id").notNull(),
  sellerId: text("seller_id").notNull(),
  images: text("images"), // JSON as text in SQLite
  contactPhone: text("contact_phone"),
  isActive: integer("is_active", { mode: 'boolean' }).default(true),
  isSold: integer("is_sold", { mode: 'boolean' }).default(false),
  viewCount: integer("view_count").default(0),
  createdAt: integer("created_at", { mode: 'timestamp' }),
  updatedAt: integer("updated_at", { mode: 'timestamp' }),
});

export const conversations = sqliteTable("conversations", {
  id: text("id").primaryKey(),
  productId: text("product_id").notNull(),
  buyerId: text("buyer_id").notNull(),
  sellerId: text("seller_id").notNull(),
  lastMessageAt: integer("last_message_at", { mode: 'timestamp' }),
  createdAt: integer("created_at", { mode: 'timestamp' }),
});

export const messages = sqliteTable("messages", {
  id: text("id").primaryKey(),
  conversationId: text("conversation_id").notNull(),
  senderId: text("sender_id").notNull(),
  content: text("content"),
  imageUrl: text("image_url"),
  messageType: text("message_type").default("text"), // 'text', 'image'
  isRead: integer("is_read", { mode: 'boolean' }).default(false),
  createdAt: integer("created_at", { mode: 'timestamp' }),
});

export const blogPosts = sqliteTable("blog_posts", {
  id: text("id").primaryKey(),
  title: text("title").notNull(),
  content: text("content").notNull(),
  excerpt: text("excerpt"),
  featuredImage: text("featured_image"),
  authorId: text("author_id").notNull(),
  isPublished: integer("is_published", { mode: 'boolean' }).default(false),
  publishedAt: integer("published_at", { mode: 'timestamp' }),
  createdAt: integer("created_at", { mode: 'timestamp' }),
  updatedAt: integer("updated_at", { mode: 'timestamp' }),
});

export const favorites = sqliteTable("favorites", {
  id: text("id").primaryKey(),
  userId: text("user_id").notNull(),
  productId: text("product_id").notNull(),
  createdAt: integer("created_at", { mode: 'timestamp' }),
});

// Relations (same as PostgreSQL version)
export const usersRelations = relations(users, ({ many }) => ({
  products: many(products),
  conversations: many(conversations),
  messages: many(messages),
  blogPosts: many(blogPosts),
  favorites: many(favorites),
}));

export const categoriesRelations = relations(categories, ({ many, one }) => ({
  products: many(products),
  parent: one(categories, {
    fields: [categories.parentId],
    references: [categories.id],
  }),
  children: many(categories),
}));

export const locationsRelations = relations(locations, ({ many }) => ({
  products: many(products),
}));

export const productsRelations = relations(products, ({ one, many }) => ({
  seller: one(users, {
    fields: [products.sellerId],
    references: [users.id],
  }),
  category: one(categories, {
    fields: [products.categoryId],
    references: [categories.id],
  }),
  location: one(locations, {
    fields: [products.locationId],
    references: [locations.id],
  }),
  conversations: many(conversations),
  favorites: many(favorites),
}));

export const conversationsRelations = relations(conversations, ({ one, many }) => ({
  product: one(products, {
    fields: [conversations.productId],
    references: [products.id],
  }),
  buyer: one(users, {
    fields: [conversations.buyerId],
    references: [users.id],
  }),
  seller: one(users, {
    fields: [conversations.sellerId],
    references: [users.id],
  }),
  messages: many(messages),
}));

export const messagesRelations = relations(messages, ({ one }) => ({
  conversation: one(conversations, {
    fields: [messages.conversationId],
    references: [conversations.id],
  }),
  sender: one(users, {
    fields: [messages.senderId],
    references: [users.id],
  }),
}));

export const blogPostsRelations = relations(blogPosts, ({ one }) => ({
  author: one(users, {
    fields: [blogPosts.authorId],
    references: [users.id],
  }),
}));

export const favoritesRelations = relations(favorites, ({ one }) => ({
  user: one(users, {
    fields: [favorites.userId],
    references: [users.id],
  }),
  product: one(products, {
    fields: [favorites.productId],
    references: [products.id],
  }),
}));

// Insert schemas
export const insertProductSchema = createInsertSchema(products).omit({
  id: true,
  sellerId: true,
  viewCount: true,
  createdAt: true,
  updatedAt: true,
});

export const insertConversationSchema = createInsertSchema(conversations).omit({
  id: true,
  lastMessageAt: true,
  createdAt: true,
});

export const insertMessageSchema = createInsertSchema(messages).omit({
  id: true,
  createdAt: true,
});

export const insertBlogPostSchema = createInsertSchema(blogPosts).omit({
  id: true,
  authorId: true,
  publishedAt: true,
  createdAt: true,
  updatedAt: true,
});

export const insertCategorySchema = createInsertSchema(categories).omit({
  id: true,
  createdAt: true,
});

export const insertLocationSchema = createInsertSchema(locations).omit({
  id: true,
  createdAt: true,
});

// Types
export type UpsertUser = typeof users.$inferInsert;
export type User = typeof users.$inferSelect;
export type Product = typeof products.$inferSelect;
export type InsertProduct = z.infer<typeof insertProductSchema>;
export type Category = typeof categories.$inferSelect;
export type InsertCategory = z.infer<typeof insertCategorySchema>;
export type Location = typeof locations.$inferSelect;
export type InsertLocation = z.infer<typeof insertLocationSchema>;
export type Conversation = typeof conversations.$inferSelect;
export type InsertConversation = z.infer<typeof insertConversationSchema>;
export type Message = typeof messages.$inferSelect;
export type InsertMessage = z.infer<typeof insertMessageSchema>;
export type BlogPost = typeof blogPosts.$inferSelect;
export type InsertBlogPost = z.infer<typeof insertBlogPostSchema>;
export type Favorite = typeof favorites.$inferSelect;

// Extended types with relations
export type ProductWithDetails = Product & {
  seller: User;
  category: Category;
  location: Location;
};

export type ConversationWithDetails = Conversation & {
  product: Product;
  buyer: User;
  seller: User;
  messages: Message[];
};

export type MessageWithSender = Message & {
  sender: User;
};

export type BlogPostWithAuthor = BlogPost & {
  author: User;
};
