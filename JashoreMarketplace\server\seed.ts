import { db } from "./db";
import { categories, locations, products, users } from "@shared/schema-sqlite";

export async function seedDatabase() {
  try {
    console.log("Seeding database...");

    // Check if categories already exist
    const existingCategories = await db.select().from(categories).limit(1);
    if (existingCategories.length === 0) {
      console.log("Adding default categories...");
      
      const defaultCategories = [
        {
          name: "Electronics",
          nameBn: "ইলেকট্রনিক্স",
          icon: "smartphone",
          slug: "electronics",
          parentId: null,
          isActive: true,
          createdAt: new Date()
        },
        {
          name: "Mobile Phones",
          nameBn: "মোবাইল ফোন",
          icon: "phone",
          slug: "mobile-phones",
          parentId: null,
          isActive: true,
          createdAt: new Date()
        },
        {
          name: "Vehicles",
          nameBn: "যানবাহন",
          icon: "car",
          slug: "vehicles",
          parentId: null,
          isActive: true,
          createdAt: new Date()
        },
        {
          name: "Home & Living",
          nameBn: "ঘর ও জীবনযাত্রা",
          icon: "home",
          slug: "home-living",
          parentId: null,
          isActive: true,
          createdAt: new Date()
        },
        {
          name: "Fashion",
          nameBn: "ফ্যাশন",
          icon: "shirt",
          slug: "fashion",
          parentId: null,
          isActive: true,
          createdAt: new Date()
        },
        {
          name: "Books & Sports",
          nameBn: "বই ও খেলাধুলা",
          icon: "book",
          slug: "books-sports",
          parentId: null,
          isActive: true,
          createdAt: new Date()
        },
        {
          name: "Jobs",
          nameBn: "চাকরি",
          icon: "briefcase",
          slug: "jobs",
          parentId: null,
          isActive: true,
          createdAt: new Date()
        },
        {
          name: "Services",
          nameBn: "সেবা",
          icon: "wrench",
          slug: "services",
          parentId: null,
          isActive: true,
          createdAt: new Date()
        }
      ];

      await db.insert(categories).values(defaultCategories);
      console.log("Categories added successfully");
    }

    // Check if we need to add more locations
    const existingLocations = await db.select().from(locations);
    const existingLocationCount = existingLocations.length;

    if (existingLocationCount < 30) { // We expect around 30+ locations total
      console.log(`Adding/updating locations (currently have ${existingLocationCount})...`);
      
      const defaultLocations = [
        {
          district: "Dhaka",
          districtBn: "ঢাকা",
          area: "Dhanmondi",
          areaBn: "ধানমন্ডি",
          isActive: true,
          createdAt: new Date()
        },
        {
          district: "Dhaka",
          districtBn: "ঢাকা",
          area: "Gulshan",
          areaBn: "গুলশান",
          isActive: true,
          createdAt: new Date()
        },
        {
          district: "Dhaka",
          districtBn: "ঢাকা",
          area: "Uttara",
          areaBn: "উত্তরা",
          isActive: true,
          createdAt: new Date()
        },
        {
          district: "Dhaka",
          districtBn: "ঢাকা",
          area: "Mirpur",
          areaBn: "মিরপুর",
          isActive: true,
          createdAt: new Date()
        },
        {
          district: "Chittagong",
          districtBn: "চট্টগ্রাম",
          area: "Agrabad",
          areaBn: "আগ্রাবাদ",
          isActive: true,
          createdAt: new Date()
        },
        {
          district: "Chittagong",
          districtBn: "চট্টগ্রাম",
          area: "Nasirabad",
          areaBn: "নাসিরাবাদ",
          isActive: true,
          createdAt: new Date()
        },
        {
          district: "Sylhet",
          districtBn: "সিলেট",
          area: "Zindabazar",
          areaBn: "জিন্দাবাজার",
          isActive: true,
          createdAt: new Date()
        },
        {
          district: "Rajshahi",
          districtBn: "রাজশাহী",
          area: "Shaheb Bazar",
          areaBn: "সাহেব বাজার",
          isActive: true,
          createdAt: new Date()
        },
        // Khulna District - All main areas
        {
          district: "Khulna",
          districtBn: "খুলনা",
          area: "Khulna Sadar",
          areaBn: "খুলনা সদর",
          isActive: true,
          createdAt: new Date()
        },
        {
          district: "Khulna",
          districtBn: "খুলনা",
          area: "Sonadanga",
          areaBn: "সোনাডাঙ্গা",
          isActive: true,
          createdAt: new Date()
        },
        {
          district: "Khulna",
          districtBn: "খুলনা",
          area: "Daulatpur",
          areaBn: "দৌলতপুর",
          isActive: true,
          createdAt: new Date()
        },
        {
          district: "Khulna",
          districtBn: "খুলনা",
          area: "Khalishpur",
          areaBn: "খালিশপুর",
          isActive: true,
          createdAt: new Date()
        },
        {
          district: "Khulna",
          districtBn: "খুলনা",
          area: "Khan Jahan Ali",
          areaBn: "খান জাহান আলী",
          isActive: true,
          createdAt: new Date()
        },
        {
          district: "Khulna",
          districtBn: "খুলনা",
          area: "Boyra",
          areaBn: "বয়রা",
          isActive: true,
          createdAt: new Date()
        },
        {
          district: "Khulna",
          districtBn: "খুলনা",
          area: "Rupsha",
          areaBn: "রূপসা",
          isActive: true,
          createdAt: new Date()
        },
        {
          district: "Khulna",
          districtBn: "খুলনা",
          area: "Phultala",
          areaBn: "ফুলতলা",
          isActive: true,
          createdAt: new Date()
        },
        {
          district: "Khulna",
          districtBn: "খুলনা",
          area: "Dighalia",
          areaBn: "দিঘলিয়া",
          isActive: true,
          createdAt: new Date()
        },
        {
          district: "Khulna",
          districtBn: "খুলনা",
          area: "Dumuria",
          areaBn: "ডুমুরিয়া",
          isActive: true,
          createdAt: new Date()
        },
        {
          district: "Khulna",
          districtBn: "খুলনা",
          area: "Batiaghata",
          areaBn: "বটিয়াঘাটা",
          isActive: true,
          createdAt: new Date()
        },
        {
          district: "Khulna",
          districtBn: "খুলনা",
          area: "Dacope",
          areaBn: "ডাকোপ",
          isActive: true,
          createdAt: new Date()
        },
        {
          district: "Khulna",
          districtBn: "খুলনা",
          area: "Koyra",
          areaBn: "কয়রা",
          isActive: true,
          createdAt: new Date()
        },
        {
          district: "Khulna",
          districtBn: "খুলনা",
          area: "Paikgachha",
          areaBn: "পাইকগাছা",
          isActive: true,
          createdAt: new Date()
        },
        {
          district: "Khulna",
          districtBn: "খুলনা",
          area: "Terokhada",
          areaBn: "তেরখাদা",
          isActive: true,
          createdAt: new Date()
        },

        // Jashore District - All main areas
        {
          district: "Jashore",
          districtBn: "যশোর",
          area: "Jashore Sadar",
          areaBn: "যশোর সদর",
          isActive: true,
          createdAt: new Date()
        },
        {
          district: "Jashore",
          districtBn: "যশোর",
          area: "Kotwali",
          areaBn: "কোতোয়ালী",
          isActive: true,
          createdAt: new Date()
        },
        {
          district: "Jashore",
          districtBn: "যশোর",
          area: "Benapole",
          areaBn: "বেনাপোল",
          isActive: true,
          createdAt: new Date()
        },
        {
          district: "Jashore",
          districtBn: "যশোর",
          area: "Sharsha",
          areaBn: "শার্শা",
          isActive: true,
          createdAt: new Date()
        },
        {
          district: "Jashore",
          districtBn: "যশোর",
          area: "Chougachha",
          areaBn: "চৌগাছা",
          isActive: true,
          createdAt: new Date()
        },
        {
          district: "Jashore",
          districtBn: "যশোর",
          area: "Jhikargachha",
          areaBn: "ঝিকরগাছা",
          isActive: true,
          createdAt: new Date()
        },
        {
          district: "Jashore",
          districtBn: "যশোর",
          area: "Keshabpur",
          areaBn: "কেশবপুর",
          isActive: true,
          createdAt: new Date()
        },
        {
          district: "Jashore",
          districtBn: "যশোর",
          area: "Manirampur",
          areaBn: "মণিরামপুর",
          isActive: true,
          createdAt: new Date()
        },
        {
          district: "Jashore",
          districtBn: "যশোর",
          area: "Abhaynagar",
          areaBn: "অভয়নগর",
          isActive: true,
          createdAt: new Date()
        },
        {
          district: "Jashore",
          districtBn: "যশোর",
          area: "Bagherpara",
          areaBn: "বাঘারপাড়া",
          isActive: true,
          createdAt: new Date()
        }
      ];

      // Insert locations one by one to avoid duplicates
      let addedCount = 0;
      for (const location of defaultLocations) {
        try {
          await db.insert(locations).values(location);
          addedCount++;
        } catch (error) {
          // Location might already exist, skip it
          console.log(`Location ${location.areaBn}, ${location.districtBn} already exists`);
        }
      }
      console.log(`Locations updated: ${addedCount} new locations added`);
    }

    // Check if we need to add sample products
    const existingProducts = await db.select().from(products);
    if (existingProducts.length < 5) { // Always ensure we have at least 5 sample products
      console.log("Adding sample products...");

      // First, ensure we have a demo user
      const demoUserId = "demo_user_seed";
      await db.insert(users).values({
        id: demoUserId,
        email: "<EMAIL>",
        firstName: "Demo",
        lastName: "User",
        phone: "***********",
        password: "demo123", // In real app, this should be hashed
        isVerified: true,
        isAdmin: false,
        lastSeen: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      }).onConflictDoNothing();

      const sampleProducts = [
        {
          id: crypto.randomUUID(),
          title: "iPhone 14 Pro Max - অসাধারণ অবস্থায়",
          description: "সম্পূর্ণ নতুনের মতো iPhone 14 Pro Max। সব ধরনের এক্সেসরিজ সহ। কোনো সমস্যা নেই। বক্স, চার্জার সব আছে।",
          price: 125000,
          condition: "used",
          categoryId: 2, // Mobile Phones
          locationId: 1, // Dhaka - Dhanmondi
          sellerId: demoUserId,
          images: "[]",
          contactPhone: "01700000001",
          isActive: true,
          isSold: false,
          viewCount: 15,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: crypto.randomUUID(),
          title: "Honda CB 150R - ২০২২ মডেল",
          description: "একদম ফ্রেশ কন্ডিশনে Honda CB 150R। মাত্র ৮০০০ কিমি চালানো। সব কাগজপত্র ঠিক আছে। ইঞ্জিন পারফেক্ট।",
          price: 185000,
          condition: "used",
          categoryId: 3, // Vehicles
          locationId: 9, // Khulna - Sonadanga
          sellerId: demoUserId,
          images: "[]",
          contactPhone: "01700000002",
          isActive: true,
          isSold: false,
          viewCount: 32,
          createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
          updatedAt: new Date(Date.now() - 24 * 60 * 60 * 1000)
        },
        {
          id: crypto.randomUUID(),
          title: "Samsung 55 inch Smart TV",
          description: "Samsung 55 inch 4K Smart TV। ২০২৩ মডেল। রিমোট, ম্যানুয়াল সব আছে। কোনো দাগ বা সমস্যা নেই।",
          price: 65000,
          condition: "used",
          categoryId: 1, // Electronics
          locationId: 11, // Jashore - Jashore Sadar
          sellerId: demoUserId,
          images: "[]",
          contactPhone: "01700000003",
          isActive: true,
          isSold: false,
          viewCount: 8,
          createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
          updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
        },
        {
          id: crypto.randomUUID(),
          title: "ডাইনিং টেবিল সেট - ৬ চেয়ার সহ",
          description: "সুন্দর কাঠের ডাইনিং টেবিল। ৬টি চেয়ার সহ। একদম নতুনের মতো অবস্থা। ঘর পরিবর্তনের কারণে বিক্রি।",
          price: 25000,
          condition: "used",
          categoryId: 4, // Home & Living
          locationId: 13, // Jashore - Benapole
          sellerId: demoUserId,
          images: "[]",
          contactPhone: "01700000004",
          isActive: true,
          isSold: false,
          viewCount: 12,
          createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
          updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
        },
        {
          id: crypto.randomUUID(),
          title: "ল্যাপটপ - Dell Inspiron 15",
          description: "Dell Inspiron 15 ল্যাপটপ। i5 প্রসেসর, 8GB RAM, 256GB SSD। অফিসের কাজের জন্য পারফেক্ট। ব্যাটারি ভালো।",
          price: 45000,
          condition: "used",
          categoryId: 1, // Electronics
          locationId: 10, // Khulna - Khulna Sadar
          sellerId: demoUserId,
          images: "[]",
          contactPhone: "01700000005",
          isActive: true,
          isSold: false,
          viewCount: 25,
          createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
          updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)
        }
      ];

      for (const product of sampleProducts) {
        try {
          await db.insert(products).values(product);
        } catch (error) {
          console.log(`Product ${product.title} might already exist`);
        }
      }

      console.log("Sample products added successfully");
    }

    console.log("Database seeding completed");
  } catch (error) {
    console.error("Error seeding database:", error);
  }
}
