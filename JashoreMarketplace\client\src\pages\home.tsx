import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import ProductCard from "@/components/ProductCard";
import CategoryNavigation from "@/components/CategoryNavigation";
import Sidebar from "@/components/Sidebar";
import ChatModal from "@/components/ChatModal";
import PostAdModal from "@/components/PostAdModal";
import {
  Search,
  Filter,
  Grid3X3,
  List,
  MapPin,
  TrendingUp,
  Heart,
  Plus,
  Star,
  Shield,
  Clock,
  Eye
} from "lucide-react";

interface ProductFilters {
  categoryId?: number;
  locationId?: number;
  minPrice?: number;
  maxPrice?: number;
  condition?: string;
  search?: string;
  page: number;
}

export default function Home() {
  const { isAuthenticated } = useAuth();
  const [filters, setFilters] = useState<ProductFilters>({ page: 1 });
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showSidebar, setShowSidebar] = useState(false);
  const [chatModalOpen, setChatModalOpen] = useState(false);
  const [postModalOpen, setPostModalOpen] = useState(false);
  const [selectedProductId, setSelectedProductId] = useState<string | null>(null);

  // Fetch products with filters
  const { data: productsData, isLoading: productsLoading } = useQuery({
    queryKey: ['/api/products', filters],
    retry: false,
  });

  // Fetch categories
  const { data: categories } = useQuery({
    queryKey: ['/api/categories'],
    retry: false,
  });

  // Fetch locations
  const { data: locations } = useQuery({
    queryKey: ['/api/locations'],
    retry: false,
  });

  const handleSearch = (query: string) => {
    setFilters(prev => ({ ...prev, search: query, page: 1 }));
  };

  const handleLocationChange = (locationId: string) => {
    setFilters(prev => ({
      ...prev,
      locationId: locationId === "all" ? undefined : parseInt(locationId),
      page: 1
    }));
  };

  const handleCategorySelect = (categoryId?: number) => {
    setFilters(prev => ({ ...prev, categoryId, page: 1 }));
  };

  const handleFiltersChange = (newFilters: any) => {
    setFilters(prev => ({ ...prev, ...newFilters, page: 1 }));
  };

  const handleChatOpen = (productId?: string) => {
    if (!isAuthenticated) {
      window.location.href = '/login';
      return;
    }
    setSelectedProductId(productId || null);
    setChatModalOpen(true);
  };

  const handlePostAdOpen = () => {
    if (!isAuthenticated) {
      window.location.href = '/login';
      return;
    }
    setPostModalOpen(true);
  };

  const products = productsData?.products || [];
  const totalProducts = productsData?.total || 0;

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-muted/10 to-background">
      <Header
        onOpenChat={() => handleChatOpen()}
        onOpenPostModal={handlePostAdOpen}
        onSearch={handleSearch}
        onLocationChange={handleLocationChange}
      />

      <main className="container mx-auto px-4 py-8 space-y-8">
        {/* Hero Banner */}
        <div className="relative overflow-hidden rounded-3xl bg-gradient-to-r from-primary/10 via-luxury/10 to-accent/10 p-8 md:p-12">
          <div className="absolute inset-0 premium-gradient opacity-5" />
          <div className="relative space-y-6">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-6">
              <div className="space-y-4">
                <Badge className="premium-gradient text-white px-4 py-2 rounded-full">
                  🔥 Featured Marketplace
                </Badge>
                <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-foreground">
                  Discover Amazing
                  <span className="block bg-gradient-to-r from-primary via-luxury to-accent bg-clip-text text-transparent">
                    Products
                  </span>
                </h1>
                <p className="text-lg text-muted-foreground max-w-2xl">
                  Browse thousands of verified products from trusted sellers across Bangladesh
                </p>
              </div>
              <div className="flex flex-col sm:flex-row gap-4">
                <Button
                  size="lg"
                  className="premium-gradient text-white rounded-xl premium-hover luxury-shadow"
                  onClick={handlePostAdOpen}
                >
                  <Plus className="mr-2 h-5 w-5" />
                  Post Your Ad
                </Button>
                <Button 
                  variant="outline"
                  size="lg"
                  className="rounded-xl premium-hover border-2"
                  onClick={() => setShowSidebar(!showSidebar)}
                >
                  <Filter className="mr-2 h-5 w-5" />
                  Filters
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Category Navigation */}
        <Card className="glass-effect border-0">
          <CardContent className="p-6">
            <CategoryNavigation onCategorySelect={handleCategorySelect} />
          </CardContent>
        </Card>

        {/* Search and Filter Bar */}
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5" />
            <Input
              placeholder="Search products..."
              className="pl-10 h-12 rounded-xl border-2 focus:border-primary"
              onChange={(e) => handleSearch(e.target.value)}
            />
          </div>
          <div className="flex gap-4">
            <Select onValueChange={handleLocationChange}>
              <SelectTrigger className="w-48 h-12 rounded-xl border-2">
                <SelectValue placeholder="Select Location" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Locations</SelectItem>
                {locations?.map((location: any) => (
                  <SelectItem key={location.id} value={location.id.toString()}>
                    {location.area}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <div className="flex rounded-xl border-2 p-1 bg-background">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('grid')}
                className="rounded-lg"
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('list')}
                className="rounded-lg"
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar */}
          {showSidebar && (
            <div className="lg:w-80">
              <Card className="glass-effect border-0 sticky top-8">
                <CardContent className="p-6">
                  <Sidebar 
                    onFiltersChange={handleFiltersChange}
                    filters={filters}
                  />
                </CardContent>
              </Card>
            </div>
          )}

          {/* Products Grid */}
          <div className="flex-1 space-y-6">
            {/* Results Header */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div className="space-y-1">
                <h2 className="text-2xl font-bold text-foreground">
                  {filters.search ? `Search Results for "${filters.search}"` : 'All Products'}
                </h2>
                <p className="text-muted-foreground">
                  {totalProducts} products found
                </p>
              </div>
              <div className="flex items-center gap-4">
                <Select defaultValue="newest">
                  <SelectTrigger className="w-48 rounded-xl">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="newest">Newest First</SelectItem>
                    <SelectItem value="price-low">Price: Low to High</SelectItem>
                    <SelectItem value="price-high">Price: High to Low</SelectItem>
                    <SelectItem value="popular">Most Popular</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Loading State */}
            {productsLoading && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[...Array(6)].map((_, i) => (
                  <Card key={i} className="animate-pulse">
                    <div className="aspect-video bg-muted rounded-t-lg" />
                    <CardContent className="p-4 space-y-2">
                      <div className="h-4 bg-muted rounded w-3/4" />
                      <div className="h-4 bg-muted rounded w-1/2" />
                      <div className="h-6 bg-muted rounded w-1/3" />
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}

            {/* Products Grid */}
            {!productsLoading && products.length > 0 && (
              <div className={
                viewMode === 'grid' 
                  ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                  : "space-y-4"
              }>
                {products.map((product: any) => (
                  <div key={product.id} className="premium-hover">
                    <ProductCard
                      product={product}
                      viewMode={viewMode}
                      onChat={() => handleChatOpen(product.id)}
                    />
                  </div>
                ))}
              </div>
            )}

            {/* Empty State */}
            {!productsLoading && products.length === 0 && (
              <Card className="glass-effect border-0">
                <CardContent className="p-12 text-center space-y-6">
                  <div className="w-24 h-24 mx-auto premium-gradient rounded-full flex items-center justify-center">
                    <Search className="h-12 w-12 text-white" />
                  </div>
                  <div className="space-y-2">
                    <h3 className="text-2xl font-bold text-foreground">No Products Found</h3>
                    <p className="text-muted-foreground max-w-md mx-auto">
                      We couldn't find any products matching your criteria. Try adjusting your filters or search terms.
                    </p>
                  </div>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <Button 
                      onClick={() => setFilters({ page: 1 })}
                      className="rounded-xl"
                    >
                      Clear All Filters
                    </Button>
                    <Button
                      variant="outline"
                      onClick={handlePostAdOpen}
                      className="rounded-xl"
                    >
                      <Plus className="mr-2 h-4 w-4" />
                      Post First Product
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Load More */}
            {!productsLoading && products.length > 0 && products.length < totalProducts && (
              <div className="text-center pt-8">
                <Button 
                  size="lg"
                  variant="outline"
                  className="rounded-xl premium-hover"
                  onClick={() => setFilters(prev => ({ ...prev, page: prev.page + 1 }))}
                >
                  Load More Products
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* Featured Section */}
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-foreground">Trending Categories</h2>
            <Button variant="ghost" className="text-primary">
              View All
            </Button>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-4">
            {categories?.slice(0, 5).map((category: any) => (
              <Card 
                key={category.id}
                className="premium-hover cursor-pointer glass-effect border-0"
                onClick={() => handleCategorySelect(category.id)}
              >
                <CardContent className="p-6 text-center space-y-3">
                  <div className="w-12 h-12 mx-auto premium-gradient rounded-xl flex items-center justify-center">
                    <div className="text-2xl">📱</div>
                  </div>
                  <div className="space-y-1">
                    <h3 className="font-semibold text-foreground">{category.name}</h3>
                    <p className="text-sm text-muted-foreground">Popular</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </main>

      <Footer />

      {/* Modals */}
      <ChatModal
        isOpen={chatModalOpen}
        onClose={() => setChatModalOpen(false)}
        productId={selectedProductId}
      />

      <PostAdModal
        isOpen={postModalOpen}
        onClose={() => setPostModalOpen(false)}
      />
    </div>
  );
}