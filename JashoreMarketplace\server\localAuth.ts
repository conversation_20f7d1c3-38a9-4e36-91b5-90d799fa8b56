import passport from "passport";
import session from "express-session";
import type { Express, RequestHandler } from "express";
import { storage } from "./storage";

// Mock user for local development
const mockUser = {
  id: "local-user-1",
  email: "<EMAIL>",
  firstName: "Test",
  lastName: "User",
  profileImageUrl: "https://via.placeholder.com/150",
  isAdmin: true,
  isVerified: true,
};

export function getSession() {
  const sessionTtl = 7 * 24 * 60 * 60 * 1000; // 1 week
  
  return session({
    secret: process.env.SESSION_SECRET!,
    resave: false,
    saveUninitialized: false,
    cookie: {
      httpOnly: true,
      secure: false, // Set to false for local development
      maxAge: sessionTtl,
    },
  });
}

export async function setupAuth(app: Express) {
  app.set("trust proxy", 1);
  app.use(getSession());
  app.use(passport.initialize());
  app.use(passport.session());

  // Create mock user in database
  try {
    await storage.upsertUser(mockUser);
  } catch (error) {
    console.log("Note: Could not create mock user, database might not be ready yet");
  }

  passport.serializeUser((user: any, cb) => cb(null, user));
  passport.deserializeUser((user: any, cb) => cb(null, user));

  // Mock login endpoint
  app.get("/api/login", (req, res) => {
    // Simulate successful login
    req.login({ 
      claims: { 
        sub: mockUser.id,
        email: mockUser.email,
        first_name: mockUser.firstName,
        last_name: mockUser.lastName,
        profile_image_url: mockUser.profileImageUrl
      },
      expires_at: Math.floor(Date.now() / 1000) + 3600 // 1 hour from now
    }, (err) => {
      if (err) {
        return res.status(500).json({ message: "Login failed" });
      }
      res.redirect("/home");
    });
  });

  // Mock callback endpoint
  app.get("/api/callback", (req, res) => {
    res.redirect("/home");
  });

  // Mock logout endpoint
  app.get("/api/logout", (req, res) => {
    req.logout(() => {
      res.redirect("/");
    });
  });
}

export const isAuthenticated: RequestHandler = async (req, res, next) => {
  if (!req.isAuthenticated()) {
    return res.status(401).json({ message: "Unauthorized" });
  }

  const user = req.user as any;
  if (!user.expires_at) {
    return res.status(401).json({ message: "Unauthorized" });
  }

  const now = Math.floor(Date.now() / 1000);
  if (now > user.expires_at) {
    return res.status(401).json({ message: "Token expired" });
  }

  return next();
};
