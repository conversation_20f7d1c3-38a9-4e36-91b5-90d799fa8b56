import { useState, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { 
  Bold, 
  Italic, 
  Underline, 
  List, 
  ListOrdered, 
  Quote, 
  Link, 
  Image as ImageIcon,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Undo,
  Redo,
  Type,
  Heading1,
  Heading2,
  Heading3
} from "lucide-react";

interface RichTextEditorProps {
  content: string;
  onChange: (content: string) => void;
  placeholder?: string;
  className?: string;
}

export default function RichTextEditor({
  content,
  onChange,
  placeholder = "এখানে লিখুন...",
  className = ""
}: RichTextEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const [isLinkModalOpen, setIsLinkModalOpen] = useState(false);
  const [linkUrl, setLinkUrl] = useState("");

  const execCommand = (command: string, value?: string) => {
    document.execCommand(command, false, value);
    updateContent();
  };

  const updateContent = () => {
    if (editorRef.current) {
      onChange(editorRef.current.innerHTML);
    }
  };

  const handleInput = () => {
    updateContent();
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const text = e.clipboardData.getData('text/plain');
    document.execCommand('insertText', false, text);
    updateContent();
  };

  const insertLink = () => {
    const selection = window.getSelection();
    if (selection && selection.toString()) {
      const url = prompt('লিংক URL দিন:');
      if (url) {
        execCommand('createLink', url);
      }
    } else {
      alert('প্রথমে টেক্সট সিলেক্ট করুন');
    }
  };

  const insertImage = () => {
    const url = prompt('ছবির URL দিন:');
    if (url) {
      execCommand('insertImage', url);
    }
  };

  const formatBlock = (tag: string) => {
    execCommand('formatBlock', tag);
  };

  const toolbarButtons = [
    {
      group: 'format',
      buttons: [
        { icon: Bold, command: 'bold', title: 'Bold (Ctrl+B)' },
        { icon: Italic, command: 'italic', title: 'Italic (Ctrl+I)' },
        { icon: Underline, command: 'underline', title: 'Underline (Ctrl+U)' },
      ]
    },
    {
      group: 'headings',
      buttons: [
        { icon: Heading1, action: () => formatBlock('h1'), title: 'Heading 1' },
        { icon: Heading2, action: () => formatBlock('h2'), title: 'Heading 2' },
        { icon: Heading3, action: () => formatBlock('h3'), title: 'Heading 3' },
        { icon: Type, action: () => formatBlock('p'), title: 'Paragraph' },
      ]
    },
    {
      group: 'lists',
      buttons: [
        { icon: List, command: 'insertUnorderedList', title: 'Bullet List' },
        { icon: ListOrdered, command: 'insertOrderedList', title: 'Numbered List' },
        { icon: Quote, action: () => formatBlock('blockquote'), title: 'Quote' },
      ]
    },
    {
      group: 'align',
      buttons: [
        { icon: AlignLeft, command: 'justifyLeft', title: 'Align Left' },
        { icon: AlignCenter, command: 'justifyCenter', title: 'Align Center' },
        { icon: AlignRight, command: 'justifyRight', title: 'Align Right' },
      ]
    },
    {
      group: 'media',
      buttons: [
        { icon: Link, action: insertLink, title: 'Insert Link' },
        { icon: ImageIcon, action: insertImage, title: 'Insert Image' },
      ]
    },
    {
      group: 'history',
      buttons: [
        { icon: Undo, command: 'undo', title: 'Undo (Ctrl+Z)' },
        { icon: Redo, command: 'redo', title: 'Redo (Ctrl+Y)' },
      ]
    }
  ];

  return (
    <div className={`border border-border rounded-lg ${className}`}>
      {/* Toolbar */}
      <div className="flex flex-wrap items-center gap-1 p-2 border-b border-border bg-muted/30">
        {toolbarButtons.map((group, groupIndex) => (
          <div key={group.group} className="flex items-center">
            {group.buttons.map((button, buttonIndex) => {
              const IconComponent = button.icon;
              return (
                <Button
                  key={buttonIndex}
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => {
                    if (button.command) {
                      execCommand(button.command);
                    } else if (button.action) {
                      button.action();
                    }
                  }}
                  title={button.title}
                  type="button"
                >
                  <IconComponent className="h-4 w-4" />
                </Button>
              );
            })}
            {groupIndex < toolbarButtons.length - 1 && (
              <Separator orientation="vertical" className="mx-1 h-6" />
            )}
          </div>
        ))}
      </div>

      {/* Editor Content */}
      <div
        ref={editorRef}
        contentEditable
        className="min-h-[200px] p-4 focus:outline-none prose prose-sm max-w-none prose-headings:text-foreground prose-p:text-foreground prose-strong:text-foreground prose-em:text-foreground prose-blockquote:text-muted-foreground prose-blockquote:border-l-border prose-ul:text-foreground prose-ol:text-foreground prose-li:text-foreground prose-a:text-primary"
        style={{
          wordBreak: 'break-word',
          overflowWrap: 'break-word'
        }}
        onInput={handleInput}
        onPaste={handlePaste}
        dangerouslySetInnerHTML={{ __html: content }}
        data-placeholder={placeholder}
      />

      <style jsx>{`
        [contenteditable]:empty:before {
          content: attr(data-placeholder);
          color: hsl(var(--muted-foreground));
          pointer-events: none;
        }
        
        [contenteditable] h1 {
          font-size: 1.875rem;
          font-weight: 700;
          line-height: 2.25rem;
          margin-top: 1rem;
          margin-bottom: 0.5rem;
        }
        
        [contenteditable] h2 {
          font-size: 1.5rem;
          font-weight: 600;
          line-height: 2rem;
          margin-top: 1rem;
          margin-bottom: 0.5rem;
        }
        
        [contenteditable] h3 {
          font-size: 1.25rem;
          font-weight: 600;
          line-height: 1.75rem;
          margin-top: 0.75rem;
          margin-bottom: 0.25rem;
        }
        
        [contenteditable] p {
          margin-bottom: 0.5rem;
          line-height: 1.6;
        }
        
        [contenteditable] blockquote {
          border-left: 4px solid hsl(var(--border));
          padding-left: 1rem;
          margin: 1rem 0;
          font-style: italic;
          color: hsl(var(--muted-foreground));
        }
        
        [contenteditable] ul, [contenteditable] ol {
          margin: 0.5rem 0;
          padding-left: 1.5rem;
        }
        
        [contenteditable] li {
          margin-bottom: 0.25rem;
        }
        
        [contenteditable] a {
          color: hsl(var(--primary));
          text-decoration: underline;
        }
        
        [contenteditable] img {
          max-width: 100%;
          height: auto;
          border-radius: 0.375rem;
          margin: 0.5rem 0;
        }
        
        [contenteditable]:focus {
          outline: none;
        }
      `}</style>
    </div>
  );
}
