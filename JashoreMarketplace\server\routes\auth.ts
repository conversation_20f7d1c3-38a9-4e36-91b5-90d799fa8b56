import { Router } from "express";
import bcrypt from "bcryptjs";
import { storage } from "../storage";

// Super Admin Configuration
const SUPER_ADMIN_CONFIG = {
  id: "super_admin_2025",
  email: "<EMAIL>",
  phone: "01700000000",
  firstName: "Super",
  lastName: "Admin",
  password: "SuperAdmin@2025!",
  role: "super_admin"
};

// Check if credentials match super admin
function isSuperAdminCredentials(phoneOrEmail: string, password: string): boolean {
  return (
    (phoneOrEmail === SUPER_ADMIN_CONFIG.email || phoneOrEmail === SUPER_ADMIN_CONFIG.phone) &&
    password === SUPER_ADMIN_CONFIG.password
  );
}

// Create super admin user object
async function createSuperAdminUser() {
  const hashedPassword = await bcrypt.hash(SUPER_ADMIN_CONFIG.password, 12);
  const now = new Date();

  return {
    id: SUPER_ADMIN_CONFIG.id,
    email: SUPER_ADMIN_CONFIG.email,
    firstName: SUPER_ADMIN_CONFIG.firstName,
    lastName: SUPER_ADMIN_CONFIG.lastName,
    phone: SUPER_ADMIN_CONFIG.phone,
    password: hashedPassword,
    isVerified: true,
    isAdmin: true,
    role: SUPER_ADMIN_CONFIG.role,
    profileImageUrl: "",
    createdAt: now,
    lastSeen: now,
    updatedAt: now
  };
}

const router = Router();

// Register endpoint
router.post("/register", async (req, res) => {
  try {
    const { firstName, lastName, phone, email, password } = req.body;

    // Validation
    if (!firstName || !phone || !password) {
      return res.status(400).json({ 
        message: "নাম, ফোন নম্বর এবং পাসওয়ার্ড প্রয়োজন" 
      });
    }

    // Check if user already exists
    const existingUser = await storage.getUserByPhoneOrEmail(phone, email);
    if (existingUser) {
      return res.status(400).json({ 
        message: "এই ফোন নম্বর বা ইমেইল দিয়ে ইতিমধ্যে অ্যাকাউন্ট আছে" 
      });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create user
    const userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const now = new Date();
    const newUser = {
      id: userId,
      firstName,
      lastName: lastName || "",
      phone,
      email: email || "",
      password: hashedPassword,
      isVerified: false,
      isAdmin: false,
      profileImageUrl: "",
      createdAt: now,
      lastSeen: now,
      updatedAt: now
    };

    await storage.upsertUser(newUser);

    // Create session
    req.login({ 
      claims: { 
        sub: userId,
        email: email || "",
        first_name: firstName,
        last_name: lastName || "",
        phone: phone,
        profile_image_url: ""
      },
      expires_at: Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60) // 7 days
    }, (err) => {
      if (err) {
        return res.status(500).json({ message: "রেজিস্ট্রেশন সফল হয়েছে কিন্তু লগইনে সমস্যা হয়েছে" });
      }
      res.json({ 
        message: "সফলভাবে রেজিস্ট্রেশন হয়েছে",
        user: {
          id: userId,
          firstName,
          lastName: lastName || "",
          phone,
          email: email || "",
          isVerified: false,
          isAdmin: false
        }
      });
    });

  } catch (error) {
    console.error("Registration error:", error);
    res.status(500).json({ message: "রেজিস্ট্রেশনে সমস্যা হয়েছে" });
  }
});

// Login endpoint
router.post("/login", async (req, res) => {
  try {
    const { phoneOrEmail, password } = req.body;

    // Validation
    if (!phoneOrEmail || !password) {
      return res.status(400).json({
        message: "ফোন নম্বর/ইমেইল এবং পাসওয়ার্ড প্রয়োজন"
      });
    }

    // Check if it's super admin credentials
    if (isSuperAdminCredentials(phoneOrEmail, password)) {
      const superAdminUser = await createSuperAdminUser();

      // Create session for super admin
      req.login({
        claims: {
          sub: superAdminUser.id,
          email: superAdminUser.email,
          first_name: superAdminUser.firstName,
          last_name: superAdminUser.lastName,
          phone: superAdminUser.phone,
          profile_image_url: superAdminUser.profileImageUrl || ""
        },
        expires_at: Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60) // 7 days
      }, (err) => {
        if (err) {
          return res.status(500).json({ message: "লগইনে সমস্যা হয়েছে" });
        }
        res.json({
          message: "সুপার অ্যাডমিন সফলভাবে লগইন হয়েছে",
          user: {
            id: superAdminUser.id,
            firstName: superAdminUser.firstName,
            lastName: superAdminUser.lastName,
            phone: superAdminUser.phone,
            email: superAdminUser.email,
            isVerified: superAdminUser.isVerified,
            isAdmin: superAdminUser.isAdmin,
            role: superAdminUser.role,
            profileImageUrl: superAdminUser.profileImageUrl
          }
        });
      });
      return;
    }

    // Find regular user
    const user = await storage.getUserByPhoneOrEmail(phoneOrEmail, phoneOrEmail);
    if (!user) {
      return res.status(401).json({
        message: "ভুল ফোন নম্বর/ইমেইল বা পাসওয়ার্ড"
      });
    }

    // Check password
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.status(401).json({
        message: "ভুল ফোন নম্বর/ইমেইল বা পাসওয়ার্ড"
      });
    }

    // Update last seen
    await storage.updateUserLastSeen(user.id);

    // Create session
    req.login({ 
      claims: { 
        sub: user.id,
        email: user.email,
        first_name: user.firstName,
        last_name: user.lastName,
        phone: user.phone,
        profile_image_url: user.profileImageUrl || ""
      },
      expires_at: Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60) // 7 days
    }, (err) => {
      if (err) {
        return res.status(500).json({ message: "লগইনে সমস্যা হয়েছে" });
      }
      res.json({ 
        message: "সফলভাবে লগইন হয়েছে",
        user: {
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
          phone: user.phone,
          email: user.email,
          isVerified: user.isVerified,
          isAdmin: user.isAdmin,
          role: user.role || "user",
          profileImageUrl: user.profileImageUrl
        }
      });
    });

  } catch (error) {
    console.error("Login error:", error);
    res.status(500).json({ message: "লগইনে সমস্যা হয়েছে" });
  }
});

export default router;
