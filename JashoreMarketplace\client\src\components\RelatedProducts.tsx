import { useQuery } from "@tanstack/react-query";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MapPin, Clock } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { bn } from "date-fns/locale";
import { hideMobileNumbers } from "@/utils/textUtils";

interface RelatedProductsProps {
  categoryId: number;
  currentProductId: string;
  onProductClick: (productId: string) => void;
}

export default function RelatedProducts({ categoryId, currentProductId, onProductClick }: RelatedProductsProps) {
  const { data: productsData, isLoading } = useQuery({
    queryKey: ['/api/products', { categoryId, limit: 4 }],
  });

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('bn-BD', {
      style: 'currency',
      currency: 'BDT',
      minimumFractionDigits: 0,
    }).format(price).replace('BDT', '৳');
  };

  const getConditionBadge = (condition: string) => {
    if (condition === 'new') {
      return <Badge className="bg-success text-white text-xs">নতুন</Badge>;
    }
    return <Badge variant="secondary" className="text-xs">ব্যবহৃত</Badge>;
  };

  const getTimeAgo = (date: string) => {
    try {
      return formatDistanceToNow(new Date(date), { 
        addSuffix: true, 
        locale: bn 
      });
    } catch {
      return 'কিছুক্ষণ আগে';
    }
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <div className="h-40 bg-muted rounded-t-lg"></div>
            <CardContent className="p-3">
              <div className="h-4 bg-muted rounded mb-2"></div>
              <div className="h-4 bg-muted rounded w-2/3 mb-2"></div>
              <div className="h-3 bg-muted rounded w-1/2"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  // Filter out current product and limit to 4 items
  const relatedProducts = productsData?.products
    ?.filter((product: any) => product.id !== currentProductId)
    ?.slice(0, 4) || [];

  if (relatedProducts.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        <p>এই ক্যাটেগরিতে আর কোনো পণ্য নেই</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
      {relatedProducts.map((product: any) => {
        const mainImage = product.images?.[0];
        
        return (
          <Card 
            key={product.id} 
            className="hover:shadow-md transition-shadow cursor-pointer group"
            onClick={() => onProductClick(product.id)}
          >
            <div className="relative">
              {mainImage ? (
                <img
                  src={mainImage}
                  alt={product.title}
                  className="w-full h-40 object-cover rounded-t-lg"
                />
              ) : (
                <div className="w-full h-40 bg-muted rounded-t-lg flex items-center justify-center">
                  <span className="text-muted-foreground text-sm">ছবি নেই</span>
                </div>
              )}
              <div className="absolute top-2 left-2">
                {getConditionBadge(product.condition)}
              </div>
            </div>
            
            <CardContent className="p-3">
              <h3 className="font-medium text-foreground mb-1 hover:text-primary line-clamp-2 text-sm">
                {hideMobileNumbers(product.title)}
              </h3>
              <p className="text-lg font-semibold text-secondary mb-2">
                {formatPrice(parseFloat(product.price))}
              </p>
              
              <div className="flex items-center justify-between text-xs text-muted-foreground mb-2">
                <div className="flex items-center">
                  <MapPin className="h-3 w-3 mr-1" />
                  <span className="truncate">{product.location?.areaBn}</span>
                </div>
                <div className="flex items-center">
                  <Clock className="h-3 w-3 mr-1" />
                  <span>{getTimeAgo(product.createdAt)}</span>
                </div>
              </div>

              <div className="flex items-center text-xs text-muted-foreground">
                <span className="truncate">
                  {product.seller?.firstName || 'নাম নেই'}
                </span>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
