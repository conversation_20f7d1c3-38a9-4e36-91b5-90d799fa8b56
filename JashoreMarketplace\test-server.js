console.log("Starting test server...");

const express = require('express');
console.log("Express imported");

const app = express();
console.log("Express app created");

app.get('/', (req, res) => {
  res.json({ message: 'Test server is running!' });
});

const port = 3000;
console.log(`Attempting to start server on port ${port}...`);
app.listen(port, () => {
  console.log(`Test server running on port ${port}`);
  console.log(`Visit http://localhost:${port} to test`);
});
