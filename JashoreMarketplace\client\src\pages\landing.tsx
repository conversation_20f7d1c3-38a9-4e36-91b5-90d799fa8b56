import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Shield,
  TrendingUp,
  Search,
  MessageCircle,
  Award,
  ChevronRight,
  CheckCircle,
  Globe,
  Smartphone
} from "lucide-react";

export default function Landing() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-muted/20 to-background">
      {/* Hero Section */}
      <div className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-primary/10 via-luxury/5 to-accent/10 opacity-50" />
        <div className="relative px-4 sm:px-6 lg:px-8 py-16 sm:py-24 lg:py-32">
          <div className="max-w-7xl mx-auto">
            <div className="text-center space-y-8">
              {/* Brand Badge */}
              <Badge className="premium-gradient text-white px-6 py-2 text-sm font-medium rounded-full luxury-shadow">
                🇧🇩 Bangladesh's Premier Marketplace
              </Badge>
              
              {/* Main Headline */}
              <h1 className="text-4xl sm:text-5xl lg:text-7xl font-bold tracking-tight">
                <span className="block text-foreground">যশোর সেল</span>
                <span className="block bg-gradient-to-r from-primary via-luxury to-accent bg-clip-text text-transparent gradient-animate">
                  বাজার
                </span>
              </h1>
              
              {/* Subtitle */}
              <p className="max-w-3xl mx-auto text-lg sm:text-xl text-muted-foreground leading-relaxed">
                Bangladesh's most trusted marketplace for buying and selling. 
                Connect with verified sellers, discover premium products, and trade with confidence.
              </p>
              
              {/* CTA Button */}
              <div className="flex justify-center">
                <Button
                  size="lg"
                  className="premium-gradient text-white px-12 py-6 text-xl font-medium rounded-xl premium-hover luxury-shadow"
                  onClick={() => window.location.href = '/home'}
                >
                  <Search className="mr-3 h-6 w-6" />
                  Find Your Products
                  <ChevronRight className="ml-3 h-6 w-6" />
                </Button>
              </div>

              {/* Additional Info */}
              <div className="text-center space-y-2 pt-4">
                <p className="text-sm text-muted-foreground">
                  Browse thousands of products from verified sellers across Bangladesh
                </p>
                <p className="text-xs text-muted-foreground">
                  • Browse products • Compare prices • Find local sellers • No registration required to browse
                </p>
              </div>
              
              {/* Trust Indicators */}
              <div className="flex flex-wrap justify-center items-center gap-8 pt-8">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <CheckCircle className="h-5 w-5 text-success" />
                  <span>50,000+ Verified Users</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Shield className="h-5 w-5 text-primary" />
                  <span>Secure Transactions</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Award className="h-5 w-5 text-accent" />
                  <span>Premium Quality</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-16 sm:py-24 bg-card">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-foreground">
              Why Choose JashoreSellBazar?
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Experience the future of online marketplace with our premium features
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Feature 1 */}
            <Card className="premium-hover glass-effect border-0">
              <CardContent className="p-8 text-center space-y-4">
                <div className="w-16 h-16 mx-auto trust-gradient rounded-2xl flex items-center justify-center">
                  <Shield className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-foreground">Verified Sellers</h3>
                <p className="text-muted-foreground">
                  All sellers are verified with government ID and phone verification for your safety
                </p>
              </CardContent>
            </Card>
            
            {/* Feature 2 */}
            <Card className="premium-hover glass-effect border-0">
              <CardContent className="p-8 text-center space-y-4">
                <div className="w-16 h-16 mx-auto premium-gradient rounded-2xl flex items-center justify-center">
                  <MessageCircle className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-foreground">Instant Messaging</h3>
                <p className="text-muted-foreground">
                  Chat directly with sellers, share images, and negotiate prices in real-time
                </p>
              </CardContent>
            </Card>
            
            {/* Feature 3 */}
            <Card className="premium-hover glass-effect border-0">
              <CardContent className="p-8 text-center space-y-4">
                <div className="w-16 h-16 mx-auto bg-gradient-to-r from-accent to-warning rounded-2xl flex items-center justify-center">
                  <Search className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-foreground">Smart Search</h3>
                <p className="text-muted-foreground">
                  Find exactly what you need with location-based search and category filters
                </p>
              </CardContent>
            </Card>
            
            {/* Feature 4 */}
            <Card className="premium-hover glass-effect border-0">
              <CardContent className="p-8 text-center space-y-4">
                <div className="w-16 h-16 mx-auto bg-gradient-to-r from-success to-primary rounded-2xl flex items-center justify-center">
                  <Smartphone className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-foreground">Mobile Optimized</h3>
                <p className="text-muted-foreground">
                  Perfect experience on all devices - desktop, tablet, and mobile
                </p>
              </CardContent>
            </Card>
            
            {/* Feature 5 */}
            <Card className="premium-hover glass-effect border-0">
              <CardContent className="p-8 text-center space-y-4">
                <div className="w-16 h-16 mx-auto bg-gradient-to-r from-luxury to-secondary rounded-2xl flex items-center justify-center">
                  <TrendingUp className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-foreground">Price Analytics</h3>
                <p className="text-muted-foreground">
                  Get market insights and price trends to make informed buying decisions
                </p>
              </CardContent>
            </Card>
            
            {/* Feature 6 */}
            <Card className="premium-hover glass-effect border-0">
              <CardContent className="p-8 text-center space-y-4">
                <div className="w-16 h-16 mx-auto bg-gradient-to-r from-warning to-accent rounded-2xl flex items-center justify-center">
                  <Globe className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-foreground">Nationwide Reach</h3>
                <p className="text-muted-foreground">
                  Connect with buyers and sellers across all districts of Bangladesh
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Statistics Section */}
      <div className="py-16 sm:py-24 bg-gradient-to-r from-primary/5 via-luxury/5 to-accent/5">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div className="space-y-2">
              <div className="text-3xl sm:text-4xl font-bold text-primary">50K+</div>
              <div className="text-sm sm:text-base text-muted-foreground">Active Users</div>
            </div>
            <div className="space-y-2">
              <div className="text-3xl sm:text-4xl font-bold text-luxury">15K+</div>
              <div className="text-sm sm:text-base text-muted-foreground">Products Listed</div>
            </div>
            <div className="space-y-2">
              <div className="text-3xl sm:text-4xl font-bold text-accent">25K+</div>
              <div className="text-sm sm:text-base text-muted-foreground">Successful Trades</div>
            </div>
            <div className="space-y-2">
              <div className="text-3xl sm:text-4xl font-bold text-success">99%</div>
              <div className="text-sm sm:text-base text-muted-foreground">Satisfaction Rate</div>
            </div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="py-16 sm:py-24 bg-card">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center space-y-8">
          <h2 className="text-3xl sm:text-4xl font-bold text-foreground">
            Ready to Start Trading?
          </h2>
          <p className="text-lg text-muted-foreground">
            Join thousands of satisfied users who trust JashoreSellBazar for their buying and selling needs
          </p>
          <div className="flex justify-center">
            <Button
              size="lg"
              className="premium-gradient text-white px-12 py-6 text-xl font-medium rounded-xl premium-hover luxury-shadow"
              onClick={() => window.location.href = '/home'}
            >
              <Search className="mr-3 h-6 w-6" />
              Start Exploring
            </Button>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-foreground text-background py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-4">
            <h3 className="text-2xl font-bold">যশোর সেল বাজার</h3>
            <p className="text-background/70 max-w-2xl mx-auto">
              Bangladesh's most trusted marketplace platform. Built with love for the people of Bangladesh.
            </p>
            <div className="flex justify-center space-x-6 pt-4">
              <Button variant="ghost" className="text-background/70 hover:text-background">
                Privacy Policy
              </Button>
              <Button variant="ghost" className="text-background/70 hover:text-background">
                Terms of Service
              </Button>
              <Button variant="ghost" className="text-background/70 hover:text-background">
                Contact Us
              </Button>
            </div>
            <div className="pt-8 border-t border-background/20">
              <p className="text-background/50">
                © 2025 JashoreSellBazar. All rights reserved.
              </p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}