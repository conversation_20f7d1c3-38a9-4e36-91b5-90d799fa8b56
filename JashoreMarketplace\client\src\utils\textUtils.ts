/**
 * Utility functions for text processing
 */

/**
 * Hides mobile numbers (11 digits) in text by replacing them with asterisks
 * @param text - The text to process
 * @returns Text with mobile numbers hidden
 */
export function hideMobileNumbers(text: string): string {
  if (!text) return text;
  
  // Pattern to match 11-digit mobile numbers
  // Matches patterns like: 01712345678, +8801712345678, 8801712345678
  const mobilePattern = /(\+?88)?0?1[3-9]\d{8}/g;
  
  return text.replace(mobilePattern, (match) => {
    // Keep first 3 digits and last 2 digits, hide the middle
    if (match.length >= 11) {
      const cleanNumber = match.replace(/^\+?88/, ''); // Remove country code
      if (cleanNumber.length === 11) {
        return `${cleanNumber.substring(0, 3)}****${cleanNumber.substring(9)}`;
      }
    }
    // For shorter matches, hide most digits
    return match.substring(0, 2) + '*'.repeat(Math.max(0, match.length - 4)) + match.substring(match.length - 2);
  });
}

/**
 * Checks if text contains mobile numbers
 * @param text - The text to check
 * @returns True if mobile numbers are found
 */
export function containsMobileNumbers(text: string): boolean {
  if (!text) return false;
  
  const mobilePattern = /(\+?88)?0?1[3-9]\d{8}/g;
  return mobilePattern.test(text);
}

/**
 * Extracts mobile numbers from text
 * @param text - The text to extract from
 * @returns Array of found mobile numbers
 */
export function extractMobileNumbers(text: string): string[] {
  if (!text) return [];
  
  const mobilePattern = /(\+?88)?0?1[3-9]\d{8}/g;
  return text.match(mobilePattern) || [];
}
