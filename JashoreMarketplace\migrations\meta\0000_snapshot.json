{"version": "6", "dialect": "sqlite", "id": "43562143-e6ee-4801-a719-b0d547b0474a", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"blog_posts": {"name": "blog_posts", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "excerpt": {"name": "excerpt", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "featured_image": {"name": "featured_image", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "author_id": {"name": "author_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_published": {"name": "is_published", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "published_at": {"name": "published_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "categories": {"name": "categories", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name_bn": {"name": "name_bn", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "icon": {"name": "icon", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"categories_slug_unique": {"name": "categories_slug_unique", "columns": ["slug"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "conversations": {"name": "conversations", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "buyer_id": {"name": "buyer_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "seller_id": {"name": "seller_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "last_message_at": {"name": "last_message_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "favorites": {"name": "favorites", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "locations": {"name": "locations", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "district": {"name": "district", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "district_bn": {"name": "district_bn", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "area": {"name": "area", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "area_bn": {"name": "area_bn", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "messages": {"name": "messages", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "conversation_id": {"name": "conversation_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "sender_id": {"name": "sender_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "message_type": {"name": "message_type", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'text'"}, "is_read": {"name": "is_read", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "products": {"name": "products", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "price": {"name": "price", "type": "real", "primaryKey": false, "notNull": true, "autoincrement": false}, "condition": {"name": "condition", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "category_id": {"name": "category_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "location_id": {"name": "location_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "seller_id": {"name": "seller_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "images": {"name": "images", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "contact_phone": {"name": "contact_phone", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "is_sold": {"name": "is_sold", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "view_count": {"name": "view_count", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "sessions": {"name": "sessions", "columns": {"sid": {"name": "sid", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "sess": {"name": "sess", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "expire": {"name": "expire", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"IDX_session_expire": {"name": "IDX_session_expire", "columns": ["expire"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "profile_image_url": {"name": "profile_image_url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_verified": {"name": "is_verified", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "is_admin": {"name": "is_admin", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "last_seen": {"name": "last_seen", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"users_email_unique": {"name": "users_email_unique", "columns": ["email"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}