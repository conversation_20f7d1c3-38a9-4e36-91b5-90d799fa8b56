import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { isUnauthorizedError } from "@/lib/authUtils";
import { useToast } from "@/hooks/use-toast";
import { api } from "@/lib/api";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  X, 
  Upload, 
  Image as ImageIcon,
  Trash2
} from "lucide-react";

interface PostAdModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function PostAdModal({ isOpen, onClose }: PostAdModalProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    price: '',
    condition: '',
    categoryId: '',
    locationId: '',
    contactPhone: '',
  });
  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  const [imagePreviews, setImagePreviews] = useState<string[]>([]);

  const { data: categories } = useQuery({
    queryKey: ['/api/categories'],
    enabled: isOpen,
  });

  const { data: locations } = useQuery({
    queryKey: ['/api/locations'],
    enabled: isOpen,
  });

  const createProductMutation = useMutation({
    mutationFn: (data: any) => api.createProduct(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/products'] });
      resetForm();
      onClose();
      toast({
        title: "বিজ্ঞাপন প্রকাশ করা হয়েছে",
        description: "আপনার বিজ্ঞাপন সফলভাবে প্রকাশ করা হয়েছে",
        duration: 3000,
      });
    },
    onError: (error) => {
      if (isUnauthorizedError(error as Error)) {
        toast({
          title: "অনুমতি নেই",
          description: "আপনি লগআউট হয়ে গেছেন। আবার লগইন করুন...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "ত্রুটি",
        description: "বিজ্ঞাপন প্রকাশ করতে পারিনি। আবার চেষ্টা করুন।",
        variant: "destructive",
      });
    },
  });

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      price: '',
      condition: '',
      categoryId: '',
      locationId: '',
      contactPhone: user?.phone || '',
    });
    setSelectedImages([]);
    setImagePreviews([]);
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleImageSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    const maxImages = 10;
    
    if (selectedImages.length + files.length > maxImages) {
      toast({
        title: "সর্বোচ্চ ছবির সংখ্যা অতিক্রম",
        description: `সর্বোচ্চ ${maxImages} টি ছবি আপলোড করতে পারবেন`,
        variant: "destructive",
      });
      return;
    }

    const validFiles = files.filter(file => {
      if (!file.type.startsWith('image/')) {
        toast({
          title: "ভুল ফাইল ফরম্যাট",
          description: "শুধুমাত্র ছবি ফাইল নির্বাচন করুন",
          variant: "destructive",
        });
        return false;
      }
      if (file.size > 10 * 1024 * 1024) { // 10MB limit
        toast({
          title: "ফাইল সাইজ বড়",
          description: "প্রতিটি ছবি ১০ MB এর কম হতে হবে",
          variant: "destructive",
        });
        return false;
      }
      return true;
    });

    setSelectedImages(prev => [...prev, ...validFiles]);
    
    // Create previews
    validFiles.forEach(file => {
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreviews(prev => [...prev, e.target?.result as string]);
      };
      reader.readAsDataURL(file);
    });
  };

  const removeImage = (index: number) => {
    setSelectedImages(prev => prev.filter((_, i) => i !== index));
    setImagePreviews(prev => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = () => {
    // Validation
    if (!formData.title.trim()) {
      toast({
        title: "শিরোনাম প্রয়োজন",
        description: "দয়া করে পণ্যের শিরোনাম দিন",
        variant: "destructive",
      });
      return;
    }

    if (!formData.description.trim()) {
      toast({
        title: "বিবরণ প্রয়োজন",
        description: "দয়া করে পণ্যের বিবরণ দিন",
        variant: "destructive",
      });
      return;
    }

    if (!formData.price || parseFloat(formData.price) <= 0) {
      toast({
        title: "সঠিক মূল্য দিন",
        description: "দয়া করে একটি বৈধ মূল্য দিন",
        variant: "destructive",
      });
      return;
    }

    if (!formData.categoryId) {
      toast({
        title: "বিভাগ নির্বাচন করুন",
        description: "দয়া করে একটি বিভাগ নির্বাচন করুন",
        variant: "destructive",
      });
      return;
    }

    if (!formData.locationId) {
      toast({
        title: "অবস্থান নির্বাচন করুন",
        description: "দয়া করে একটি অবস্থান নির্বাচন করুন",
        variant: "destructive",
      });
      return;
    }

    if (!formData.condition) {
      toast({
        title: "অবস্থা নির্বাচন করুন",
        description: "দয়া করে পণ্যের অবস্থা নির্বাচন করুন",
        variant: "destructive",
      });
      return;
    }

    const productData = {
      ...formData,
      price: parseFloat(formData.price),
      categoryId: parseInt(formData.categoryId),
      locationId: parseInt(formData.locationId),
      images: selectedImages,
    };

    createProductMutation.mutate(productData);
  };

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            নতুন বিজ্ঞাপন দিন
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="title">শিরোনাম *</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                placeholder="আপনার পণ্যের শিরোনাম লিখুন"
                maxLength={200}
              />
              <div className="text-xs text-muted-foreground mt-1">
                {formData.title.length}/200
              </div>
            </div>

            <div>
              <Label htmlFor="description">বিবরণ *</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="পণ্যের বিস্তারিত বিবরণ লিখুন..."
                rows={4}
                maxLength={1000}
              />
              <div className="text-xs text-muted-foreground mt-1">
                {formData.description.length}/1000
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="price">মূল্য (টাকা) *</Label>
                <Input
                  id="price"
                  type="number"
                  value={formData.price}
                  onChange={(e) => handleInputChange('price', e.target.value)}
                  placeholder="0"
                  min="0"
                  step="0.01"
                />
              </div>

              <div>
                <Label htmlFor="condition">অবস্থা *</Label>
                <Select value={formData.condition} onValueChange={(value) => handleInputChange('condition', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="অবস্থা নির্বাচন করুন" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="new">নতুন</SelectItem>
                    <SelectItem value="used">ব্যবহৃত</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="category">বিভাগ *</Label>
                <Select value={formData.categoryId} onValueChange={(value) => handleInputChange('categoryId', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="বিভাগ নির্বাচন করুন" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories?.map((category: any) => (
                      <SelectItem key={category.id} value={category.id.toString()}>
                        {category.nameBn}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="location">অবস্থান *</Label>
                <Select value={formData.locationId} onValueChange={(value) => handleInputChange('locationId', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="অবস্থান নির্বাচন করুন" />
                  </SelectTrigger>
                  <SelectContent>
                    {locations?.map((location: any) => (
                      <SelectItem key={location.id} value={location.id.toString()}>
                        {location.areaBn}, {location.districtBn}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="contactPhone">যোগাযোগের নম্বর</Label>
              <Input
                id="contactPhone"
                type="tel"
                value={formData.contactPhone}
                onChange={(e) => handleInputChange('contactPhone', e.target.value)}
                placeholder="আপনার মোবাইল নম্বর"
              />
            </div>
          </div>

          {/* Image Upload */}
          <div>
            <Label>ছবি আপলোড করুন</Label>
            <div className="border-2 border-dashed border-border rounded-lg p-6 text-center">
              <div className="space-y-4">
                <div className="text-muted-foreground">
                  <ImageIcon className="h-12 w-12 mx-auto mb-2" />
                  <p>ছবি ড্র্যাগ করুন অথবা ক্লিক করে নির্বাচন করুন</p>
                  <p className="text-sm">সর্বোচ্চ ১০টি ছবি, প্রতিটি ১০ MB এর কম</p>
                </div>
                <input
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={handleImageSelect}
                  className="hidden"
                  id="image-upload"
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => document.getElementById('image-upload')?.click()}
                >
                  <Upload className="h-4 w-4 mr-2" />
                  ছবি নির্বাচন করুন
                </Button>
              </div>
            </div>

            {/* Image Previews */}
            {imagePreviews.length > 0 && (
              <div className="grid grid-cols-3 md:grid-cols-5 gap-4 mt-4">
                {imagePreviews.map((preview, index) => (
                  <div key={index} className="relative group">
                    <img
                      src={preview}
                      alt={`Preview ${index + 1}`}
                      className="w-full h-24 object-cover rounded border"
                    />
                    <button
                      type="button"
                      onClick={() => removeImage(index)}
                      className="absolute top-1 right-1 bg-destructive text-destructive-foreground rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <Trash2 className="h-3 w-3" />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-4 pt-4">
            <Button
              onClick={onClose}
              variant="outline"
              className="flex-1"
              disabled={createProductMutation.isPending}
            >
              বাতিল
            </Button>
            <Button
              onClick={handleSubmit}
              className="flex-1 bg-secondary text-secondary-foreground hover:bg-secondary/90"
              disabled={createProductMutation.isPending}
            >
              {createProductMutation.isPending ? 'প্রকাশ করা হচ্ছে...' : 'বিজ্ঞাপন প্রকাশ করুন'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
