import { useState, useEffect } from "react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { api } from "@/lib/api";

interface EditProductModalProps {
  isOpen: boolean;
  onClose: () => void;
  product: any;
}

export default function EditProductModal({ isOpen, onClose, product }: EditProductModalProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    price: '',
    condition: '',
    categoryId: '',
    locationId: '',
    contactPhone: '',
  });

  const { data: categories } = useQuery({
    queryKey: ['/api/categories'],
  });

  const { data: locations } = useQuery({
    queryKey: ['/api/locations'],
  });

  useEffect(() => {
    if (product && isOpen) {
      setFormData({
        title: product.title || '',
        description: product.description || '',
        price: product.price?.toString() || '',
        condition: product.condition || '',
        categoryId: product.categoryId?.toString() || '',
        locationId: product.locationId?.toString() || '',
        contactPhone: product.contactPhone || '',
      });
    }
  }, [product, isOpen]);

  const updateProductMutation = useMutation({
    mutationFn: (data: any) => api.updateProduct(product.id, data),
    onSuccess: () => {
      toast({
        title: "সফল",
        description: "পণ্য সফলভাবে আপডেট হয়েছে",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/users'] });
      queryClient.invalidateQueries({ queryKey: [`/api/products/${product.id}`] });
      onClose();
    },
    onError: (error: any) => {
      toast({
        title: "ত্রুটি",
        description: error.message || "পণ্য আপডেট করতে পারিনি",
        variant: "destructive",
      });
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title || !formData.description || !formData.price) {
      toast({
        title: "ত্রুটি",
        description: "সকল প্রয়োজনীয় ক্ষেত্র পূরণ করুন",
        variant: "destructive",
      });
      return;
    }

    const updateData = {
      title: formData.title,
      description: formData.description,
      price: parseFloat(formData.price),
      condition: formData.condition,
      categoryId: parseInt(formData.categoryId),
      locationId: parseInt(formData.locationId),
      contactPhone: formData.contactPhone,
    };

    updateProductMutation.mutate(updateData);
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>পণ্য সম্পাদনা করুন</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Title */}
          <div className="space-y-2">
            <Label htmlFor="title">পণ্যের নাম *</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              placeholder="পণ্যের নাম লিখুন"
              required
            />
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">বিবরণ *</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="পণ্যের বিস্তারিত বিবরণ লিখুন"
              rows={4}
              required
            />
          </div>

          {/* Price */}
          <div className="space-y-2">
            <Label htmlFor="price">দাম (টাকা) *</Label>
            <Input
              id="price"
              type="number"
              value={formData.price}
              onChange={(e) => handleInputChange('price', e.target.value)}
              placeholder="দাম লিখুন"
              required
            />
          </div>

          {/* Category and Location */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="category">ক্যাটেগরি *</Label>
              <Select value={formData.categoryId} onValueChange={(value) => handleInputChange('categoryId', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="ক্যাটেগরি নির্বাচন করুন" />
                </SelectTrigger>
                <SelectContent>
                  {categories?.map((category: any) => (
                    <SelectItem key={category.id} value={category.id.toString()}>
                      {category.nameBn}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="location">এলাকা *</Label>
              <Select value={formData.locationId} onValueChange={(value) => handleInputChange('locationId', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="এলাকা নির্বাচন করুন" />
                </SelectTrigger>
                <SelectContent>
                  {locations?.map((location: any) => (
                    <SelectItem key={location.id} value={location.id.toString()}>
                      {location.districtBn}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Condition */}
          <div className="space-y-2">
            <Label htmlFor="condition">অবস্থা *</Label>
            <Select value={formData.condition} onValueChange={(value) => handleInputChange('condition', value)}>
              <SelectTrigger>
                <SelectValue placeholder="পণ্যের অবস্থা নির্বাচন করুন" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="নতুন">নতুন</SelectItem>
                <SelectItem value="প্রায় নতুন">প্রায় নতুন</SelectItem>
                <SelectItem value="ভালো">ভালো</SelectItem>
                <SelectItem value="গ্রহণযোগ্য">গ্রহণযোগ্য</SelectItem>
                <SelectItem value="মেরামতের প্রয়োজন">মেরামতের প্রয়োজন</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Contact Phone */}
          <div className="space-y-2">
            <Label htmlFor="contactPhone">যোগাযোগের ফোন নম্বর</Label>
            <Input
              id="contactPhone"
              value={formData.contactPhone}
              onChange={(e) => handleInputChange('contactPhone', e.target.value)}
              placeholder="যোগাযোগের জন্য ফোন নম্বর"
            />
          </div>

          {/* Submit Buttons */}
          <div className="flex justify-end space-x-3 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              বাতিল
            </Button>
            <Button 
              type="submit" 
              disabled={updateProductMutation.isPending}
              className="bg-primary text-primary-foreground hover:bg-primary/90"
            >
              {updateProductMutation.isPending ? "আপডেট হচ্ছে..." : "আপডেট করুন"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
