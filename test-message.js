// Test script to verify message creation fix
import { storage } from './server/storage.js';

async function testMessageCreation() {
  try {
    console.log('Testing message creation...');
    
    // Test data
    const messageData = {
      conversationId: 'test-conversation-id',
      senderId: 'test-user-id',
      content: 'Test message',
      messageType: 'text'
    };
    
    const result = await storage.createMessage(messageData);
    console.log('Message created successfully:', result);
    
  } catch (error) {
    console.error('Error creating message:', error);
  }
}

testMessageCreation();
