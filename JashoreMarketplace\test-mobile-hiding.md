# Mobile Number Hiding Test

## Test Cases:

1. **Product Title**: "iPhone 15 Pro Max for sale call 01712345678"
   - **Expected**: "iPhone 15 Pro Max for sale call 017****78"

2. **Product Description**: "Brand new iPhone 15 Pro Max. Contact me at +8801712345678 or 01987654321 for details."
   - **Expected**: "Brand new iPhone 15 Pro Max. Contact me at +88017****78 or 019****21 for details."

3. **Various Formats**:
   - `01712345678` → `017****78`
   - `+8801712345678` → `+88017****78`
   - `8801712345678` → `88017****78`

## How it works:

The `hideMobileNumbers()` function:
1. Detects 11-digit mobile numbers in various formats
2. Keeps first 3 digits and last 2 digits visible
3. Replaces middle digits with asterisks (****)
4. Works with country codes (+88, 88) and without

## Applied to:
- ✅ Product titles in cards
- ✅ Product titles in details page
- ✅ Product descriptions
- ✅ Related products
- ✅ All product listings
