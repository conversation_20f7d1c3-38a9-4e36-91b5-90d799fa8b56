import { use<PERSON><PERSON><PERSON>, <PERSON> } from "wouter";
import { useQuery } from "@tanstack/react-query";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { Calendar, User, ArrowLeft, Clock } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { bn } from "date-fns/locale";

export default function Blog() {
  const { id } = useParams();

  const { data: blogPosts, isLoading: postsLoading } = useQuery({
    queryKey: ['/api/blog'],
    enabled: !id, // Only fetch all posts if not viewing a specific post
  });

  const { data: blogPost, isLoading: postLoading } = useQuery({
    queryKey: [`/api/blog/${id}`],
    enabled: !!id, // Only fetch specific post if id is present
  });

  const getTimeAgo = (date?: string) => {
    if (!date) return 'তারিখ নেই';
    try {
      return formatDistanceToNow(new Date(date), { 
        addSuffix: true, 
        locale: bn 
      });
    } catch {
      return 'কিছুক্ষণ আগে';
    }
  };

  const getInitials = (firstName?: string, lastName?: string) => {
    return `${firstName?.[0] || ''}${lastName?.[0] || ''}`.toUpperCase() || 'A';
  };

  // Single blog post view
  if (id) {
    if (postLoading) {
      return (
        <div className="min-h-screen bg-background">
          <Header 
            onOpenChat={() => {}}
            onOpenPostModal={() => {}}
            onSearch={() => {}}
            onLocationChange={() => {}}
          />
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="animate-pulse space-y-6">
              <div className="bg-muted h-8 rounded w-1/3"></div>
              <div className="bg-muted h-64 rounded"></div>
              <div className="space-y-4">
                <div className="bg-muted h-4 rounded"></div>
                <div className="bg-muted h-4 rounded w-5/6"></div>
                <div className="bg-muted h-4 rounded w-4/5"></div>
              </div>
            </div>
          </div>
        </div>
      );
    }

    if (!blogPost) {
      return (
        <div className="min-h-screen bg-background">
          <Header 
            onOpenChat={() => {}}
            onOpenPostModal={() => {}}
            onSearch={() => {}}
            onLocationChange={() => {}}
          />
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="text-center py-12">
              <h1 className="text-2xl font-bold text-foreground mb-4">ব্লগ পোস্ট পাওয়া যায়নি</h1>
              <Link href="/blog">
                <a className="text-primary hover:underline">ব্লগ পেজে ফিরে যান</a>
              </Link>
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="min-h-screen bg-background">
        <Header 
          onOpenChat={() => {}}
          onOpenPostModal={() => {}}
          onSearch={() => {}}
          onLocationChange={() => {}}
        />

        <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          {/* Back Navigation */}
          <Link href="/blog">
            <div className="flex items-center text-muted-foreground hover:text-primary cursor-pointer mb-6">
              <ArrowLeft className="h-4 w-4 mr-2" />
              ব্লগে ফিরে যান
            </div>
          </Link>

          <article>
            {/* Featured Image */}
            {blogPost.featuredImage && (
              <div className="mb-8">
                <img
                  src={blogPost.featuredImage}
                  alt={blogPost.title}
                  className="w-full h-64 md:h-96 object-cover rounded-lg"
                />
              </div>
            )}

            {/* Post Header */}
            <header className="mb-8">
              <h1 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
                {blogPost.title}
              </h1>

              <div className="flex items-center space-x-4 text-sm text-muted-foreground mb-4">
                <div className="flex items-center">
                  <Avatar className="h-8 w-8 mr-2">
                    <AvatarImage 
                      src={blogPost.author?.profileImageUrl || undefined}
                      className="object-cover"
                    />
                    <AvatarFallback className="bg-primary text-primary-foreground text-xs">
                      {getInitials(blogPost.author?.firstName, blogPost.author?.lastName)}
                    </AvatarFallback>
                  </Avatar>
                  <span>{blogPost.author?.firstName} {blogPost.author?.lastName}</span>
                </div>
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-1" />
                  <span>{getTimeAgo(blogPost.publishedAt || blogPost.createdAt)}</span>
                </div>
              </div>

              {blogPost.excerpt && (
                <p className="text-lg text-muted-foreground">
                  {blogPost.excerpt}
                </p>
              )}
            </header>

            <Separator className="mb-8" />

            {/* Post Content */}
            <div 
              className="prose prose-lg max-w-none prose-headings:text-foreground prose-p:text-foreground prose-strong:text-foreground prose-a:text-primary"
              dangerouslySetInnerHTML={{ __html: blogPost.content }}
            />
          </article>

          {/* Author Bio */}
          <Separator className="my-12" />
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <Avatar className="h-16 w-16">
                  <AvatarImage 
                    src={blogPost.author?.profileImageUrl || undefined}
                    className="object-cover"
                  />
                  <AvatarFallback className="bg-primary text-primary-foreground">
                    {getInitials(blogPost.author?.firstName, blogPost.author?.lastName)}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h3 className="font-semibold text-lg">
                    {blogPost.author?.firstName} {blogPost.author?.lastName}
                  </h3>
                  <p className="text-muted-foreground">
                    ব্লগ লেখক
                  </p>
                  {blogPost.author?.isVerified && (
                    <Badge className="bg-success text-white mt-1">
                      যাচাইকৃত লেখক
                    </Badge>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </main>

        <Footer />
      </div>
    );
  }

  // Blog list view
  return (
    <div className="min-h-screen bg-background">
      <Header 
        onOpenChat={() => {}}
        onOpenPostModal={() => {}}
        onSearch={() => {}}
        onLocationChange={() => {}}
      />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Blog Header */}
        <div className="text-center mb-12">
          <h1 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
            JashoreSellBazar ব্লগ
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            অনলাইন কেনাকাটা, নিরাপত্তা টিপস, এবং মার্কেটপ্লেস সম্পর্কিত সকল তথ্য পান
          </p>
        </div>

        {/* Blog Posts Grid */}
        {postsLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {Array.from({ length: 6 }).map((_, i) => (
              <Card key={i} className="animate-pulse">
                <div className="bg-muted h-48 rounded-t-lg"></div>
                <CardContent className="p-6 space-y-4">
                  <div className="bg-muted h-4 rounded"></div>
                  <div className="bg-muted h-4 rounded w-5/6"></div>
                  <div className="bg-muted h-3 rounded w-1/2"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : blogPosts?.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {blogPosts.map((post: any) => (
              <Link key={post.id} href={`/blog/${post.id}`}>
                <Card className="hover:shadow-lg transition-shadow cursor-pointer h-full">
                  {post.featuredImage && (
                    <div className="relative">
                      <img
                        src={post.featuredImage}
                        alt={post.title}
                        className="w-full h-48 object-cover rounded-t-lg"
                      />
                    </div>
                  )}
                  <CardContent className="p-6">
                    <h2 className="text-xl font-semibold text-foreground mb-2 line-clamp-2">
                      {post.title}
                    </h2>
                    {post.excerpt && (
                      <p className="text-muted-foreground mb-4 line-clamp-3">
                        {post.excerpt}
                      </p>
                    )}
                    <div className="flex items-center justify-between text-sm text-muted-foreground">
                      <div className="flex items-center">
                        <User className="h-4 w-4 mr-1" />
                        <span>{post.author?.firstName}</span>
                      </div>
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 mr-1" />
                        <span>{getTimeAgo(post.publishedAt || post.createdAt)}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">📝</div>
            <h2 className="text-2xl font-bold text-foreground mb-4">
              কোন ব্লগ পোস্ট নেই
            </h2>
            <p className="text-muted-foreground">
              শীঘ্রই আমাদের ব্লগে দরকারী তথ্য প্রকাশ করা হবে
            </p>
          </div>
        )}

        {/* Newsletter Signup */}
        <Card className="mt-16">
          <CardHeader className="text-center">
            <h2 className="text-2xl font-bold text-foreground mb-2">
              নতুন পোস্টের জন্য সাবস্ক্রাইব করুন
            </h2>
            <p className="text-muted-foreground">
              আমাদের নতুন ব্লগ পোস্ট সম্পর্কে জানতে চান?
            </p>
          </CardHeader>
          <CardContent className="text-center">
            <div className="max-w-md mx-auto flex space-x-2">
              <input
                type="email"
                placeholder="আপনার ইমেইল ঠিকানা"
                className="flex-1 px-4 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
              />
              <button className="bg-primary text-primary-foreground px-6 py-2 rounded-lg hover:bg-primary/90 transition-colors">
                সাবস্ক্রাইব
              </button>
            </div>
          </CardContent>
        </Card>
      </main>

      <Footer />
    </div>
  );
}
