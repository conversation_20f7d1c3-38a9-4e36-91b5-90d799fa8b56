console.log("=== DEBUG SERVER START ===");
console.log("Node version:", process.version);
console.log("Platform:", process.platform);
console.log("Current directory:", process.cwd());

try {
  console.log("Importing express...");
  const express = require('express');
  console.log("Express imported successfully");
  console.log("Express version:", express.version || "unknown");
  
  console.log("Creating app...");
  const app = express();
  console.log("App created successfully");
  
  app.get('/', (req, res) => {
    console.log("Received request to /");
    res.json({ message: 'Debug server is working!' });
  });
  
  const port = 3000;
  console.log(`Starting server on port ${port}...`);
  
  const server = app.listen(port, (err) => {
    if (err) {
      console.error("Error starting server:", err);
      process.exit(1);
    }
    console.log(`✅ Debug server running on http://localhost:${port}`);
    console.log("Server is ready to accept connections");
  });
  
  server.on('error', (err) => {
    console.error("Server error:", err);
  });
  
  // Keep the process alive
  process.on('SIGINT', () => {
    console.log("Received SIGINT, shutting down gracefully");
    server.close(() => {
      console.log("Server closed");
      process.exit(0);
    });
  });
  
} catch (error) {
  console.error("Error in debug server:", error);
  process.exit(1);
}

console.log("=== DEBUG SERVER SETUP COMPLETE ===");
