import { Link } from "wouter";
import { Store, Facebook, Twitter, Instagram, Phone, Mail, MapPin } from "lucide-react";

export default function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-foreground text-white mt-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <div className="flex items-center">
              <Store className="h-6 w-6 mr-2" />
              <div className="text-xl font-bold">JashoreSellBazar</div>
            </div>
            <p className="text-gray-300 text-sm leading-relaxed">
              বাংলাদেশের সবচেয়ে বিশ্বস্ত অনলাইন মার্কেটপ্লেস। 
              নিরাপদে কিনুন ও বিক্রি করুন যশোর এবং সারা বাংলাদেশে।
            </p>
            
            {/* Contact Info */}
            <div className="space-y-2">
              <div className="flex items-center text-sm text-gray-300">
                <Phone className="h-4 w-4 mr-2" />
                <span>+৮৮০ ১৭১১ ১২৩৪৫৬</span>
              </div>
              <div className="flex items-center text-sm text-gray-300">
                <Mail className="h-4 w-4 mr-2" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center text-sm text-gray-300">
                <MapPin className="h-4 w-4 mr-2" />
                <span>যশোর, বাংলাদেশ</span>
              </div>
            </div>

            {/* Social Media */}
            <div className="flex space-x-4">
              <a
                href="#"
                className="text-gray-300 hover:text-white transition-colors"
                aria-label="Facebook"
              >
                <Facebook className="h-5 w-5" />
              </a>
              <a
                href="#"
                className="text-gray-300 hover:text-white transition-colors"
                aria-label="Twitter"
              >
                <Twitter className="h-5 w-5" />
              </a>
              <a
                href="#"
                className="text-gray-300 hover:text-white transition-colors"
                aria-label="Instagram"
              >
                <Instagram className="h-5 w-5" />
              </a>
            </div>
          </div>

          {/* Support Links */}
          <div>
            <h3 className="font-semibold mb-4">সহায়তা</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/contact">
                  <span className="text-gray-300 hover:text-white transition-colors cursor-pointer">
                    যোগাযোগ
                  </span>
                </Link>
              </li>
              <li>
                <Link href="/help">
                  <span className="text-gray-300 hover:text-white transition-colors cursor-pointer">
                    সাহায্য কেন্দ্র
                  </span>
                </Link>
              </li>
              <li>
                <Link href="/safety">
                  <span className="text-gray-300 hover:text-white transition-colors cursor-pointer">
                    নিরাপত্তা টিপস
                  </span>
                </Link>
              </li>
              <li>
                <Link href="/terms">
                  <span className="text-gray-300 hover:text-white transition-colors cursor-pointer">
                    নিয়ম ও শর্তাবলী
                  </span>
                </Link>
              </li>
              <li>
                <Link href="/privacy">
                  <span className="text-gray-300 hover:text-white transition-colors cursor-pointer">
                    গোপনীয়তা নীতি
                  </span>
                </Link>
              </li>
              <li>
                <Link href="/faq">
                  <span className="text-gray-300 hover:text-white transition-colors cursor-pointer">
                    প্রশ্ন ও উত্তর
                  </span>
                </Link>
              </li>
            </ul>
          </div>

          {/* Popular Categories */}
          <div>
            <h3 className="font-semibold mb-4">জনপ্রিয় বিভাগ</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/products?category=1">
                  <span className="text-gray-300 hover:text-white transition-colors cursor-pointer">
                    মোবাইল ও ট্যাবলেট
                  </span>
                </Link>
              </li>
              <li>
                <Link href="/products?category=2">
                  <span className="text-gray-300 hover:text-white transition-colors cursor-pointer">
                    গাড়ি ও যানবাহন
                  </span>
                </Link>
              </li>
              <li>
                <Link href="/products?category=3">
                  <span className="text-gray-300 hover:text-white transition-colors cursor-pointer">
                    ঘরবাড়ি ও জমি
                  </span>
                </Link>
              </li>
              <li>
                <Link href="/products?category=1">
                  <span className="text-gray-300 hover:text-white transition-colors cursor-pointer">
                    ইলেকট্রনিক্স
                  </span>
                </Link>
              </li>
              <li>
                <Link href="/products?category=4">
                  <span className="text-gray-300 hover:text-white transition-colors cursor-pointer">
                    ফ্যাশন ও লাইফস্টাইল
                  </span>
                </Link>
              </li>
              <li>
                <Link href="/products?category=5">
                  <span className="text-gray-300 hover:text-white transition-colors cursor-pointer">
                    চাকরি ও ক্যারিয়ার
                  </span>
                </Link>
              </li>
            </ul>
          </div>

          {/* Download Apps & Quick Links */}
          <div>
            <h3 className="font-semibold mb-4">অ্যাপ ডাউনলোড</h3>
            <div className="space-y-3 mb-6">
              <a
                href="#"
                className="flex items-center space-x-2 text-gray-300 hover:text-white transition-colors"
              >
                <div className="w-8 h-8 bg-gray-600 rounded flex items-center justify-center">
                  <span className="text-xs font-bold">GP</span>
                </div>
                <div>
                  <div className="text-xs">Google Play থেকে</div>
                  <div className="text-sm font-medium">ডাউনলোড করুন</div>
                </div>
              </a>
              <a
                href="#"
                className="flex items-center space-x-2 text-gray-300 hover:text-white transition-colors"
              >
                <div className="w-8 h-8 bg-gray-600 rounded flex items-center justify-center">
                  <span className="text-xs font-bold">AS</span>
                </div>
                <div>
                  <div className="text-xs">App Store থেকে</div>
                  <div className="text-sm font-medium">ডাউনলোড করুন</div>
                </div>
              </a>
            </div>

            <div>
              <h4 className="font-medium mb-2 text-sm">দ্রুত লিংক</h4>
              <ul className="space-y-1 text-sm">
                <li>
                  <Link href="/blog">
                    <span className="text-gray-300 hover:text-white transition-colors cursor-pointer">
                      ব্লগ
                    </span>
                  </Link>
                </li>
                <li>
                  <Link href="/about">
                    <span className="text-gray-300 hover:text-white transition-colors cursor-pointer">
                      আমাদের সম্পর্কে
                    </span>
                  </Link>
                </li>
                <li>
                  <Link href="/careers">
                    <span className="text-gray-300 hover:text-white transition-colors cursor-pointer">
                      ক্যারিয়ার
                    </span>
                  </Link>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-700 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-center md:text-left text-sm text-gray-300 mb-4 md:mb-0">
              <p>
                &copy; {currentYear} JashoreSellBazar. সমস্ত অধিকার সংরক্ষিত।
              </p>
              <p className="mt-1">
                একটি নিরাপদ ও বিশ্বস্ত অনলাইন মার্কেটপ্লেস
              </p>
            </div>
            
            <div className="flex items-center space-x-6 text-sm text-gray-300">
              <span className="flex items-center">
                <span className="w-2 h-2 bg-success rounded-full mr-2"></span>
                SSL সিকিউর
              </span>
              <span className="flex items-center">
                <span className="w-2 h-2 bg-success rounded-full mr-2"></span>
                ২৪/৭ সাপোর্ট
              </span>
              <span className="flex items-center">
                <span className="w-2 h-2 bg-success rounded-full mr-2"></span>
                যাচাইকৃত বিক্রেতা
              </span>
            </div>
          </div>

          {/* Legal Notice */}
          <div className="mt-4 pt-4 border-t border-gray-800 text-center text-xs text-gray-400">
            <p>
              JashoreSellBazar একটি অনলাইন প্ল্যাটফর্ম যেখানে ব্যবহারকারীরা পণ্য কিনতে ও বিক্রি করতে পারেন।
              আমরা শুধুমাত্র প্ল্যাটফর্ম সেবা প্রদান করি এবং লেনদেনের দায়ভার ক্রেতা-বিক্রেতার উপর বর্তায়।
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}
