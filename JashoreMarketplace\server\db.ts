import dotenv from "dotenv";
dotenv.config();

import { drizzle as drizzleSqlite } from 'drizzle-orm/better-sqlite3';
import Database from 'better-sqlite3';

if (!process.env.DATABASE_URL) {
  throw new Error(
    "DATABASE_URL must be set. Did you forget to provision a database?",
  );
}

console.log('DATABASE_URL:', process.env.DATABASE_URL);

// For now, let's just use SQLite for local development
const dbPath = process.env.DATABASE_URL.replace('file:', '');
console.log('Creating SQLite database at:', dbPath);

const sqlite = new Database(dbPath);
console.log('SQLite database created successfully');

// Create a simple database without schema for now
const db = drizzleSqlite(sqlite);
console.log('Drizzle initialized successfully');

export { db };