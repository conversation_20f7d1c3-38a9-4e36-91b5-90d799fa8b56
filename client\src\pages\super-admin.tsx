import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { 
  Users, 
  Package, 
  Trash2, 
  Shield, 
  Eye,
  Calendar,
  Phone,
  Mail,
  MapPin,
  Star,
  Clock
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { bn } from "date-fns/locale";

export default function SuperAdmin() {
  const { user, isAuthenticated, isLoading } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [selectedTab, setSelectedTab] = useState("users");

  useEffect(() => {
    if (!isLoading && (!isAuthenticated || user?.role !== "super_admin")) {
      toast({
        title: "অনুমতি নেই",
        description: "আপনার সুপার অ্যাডমিন অ্যাক্সেস নেই",
        variant: "destructive",
      });
      setTimeout(() => {
        window.location.href = "/";
      }, 500);
      return;
    }
  }, [isAuthenticated, isLoading, user, toast]);

  const { data: users, isLoading: usersLoading } = useQuery({
    queryKey: ['/api/super-admin/users'],
    enabled: isAuthenticated && user?.role === "super_admin",
  });

  const { data: productsData, isLoading: productsLoading } = useQuery({
    queryKey: ['/api/super-admin/products'],
    enabled: isAuthenticated && user?.role === "super_admin",
  });

  const deleteUserMutation = useMutation({
    mutationFn: async (userId: string) => {
      const response = await fetch(`/api/super-admin/users/${userId}`, {
        method: 'DELETE',
        credentials: 'include',
      });
      if (!response.ok) throw new Error('Failed to delete user');
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "সফল",
        description: "ব্যবহারকারী সফলভাবে মুছে ফেলা হয়েছে",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/super-admin/users'] });
    },
    onError: () => {
      toast({
        title: "ত্রুটি",
        description: "ব্যবহারকারী মুছে ফেলতে পারিনি",
        variant: "destructive",
      });
    },
  });

  const deleteProductMutation = useMutation({
    mutationFn: async (productId: string) => {
      const response = await fetch(`/api/super-admin/products/${productId}`, {
        method: 'DELETE',
        credentials: 'include',
      });
      if (!response.ok) throw new Error('Failed to delete product');
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "সফল",
        description: "পণ্য সফলভাবে মুছে ফেলা হয়েছে",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/super-admin/products'] });
    },
    onError: () => {
      toast({
        title: "ত্রুটি",
        description: "পণ্য মুছে ফেলতে পারিনি",
        variant: "destructive",
      });
    },
  });

  const getInitials = (firstName?: string, lastName?: string) => {
    return `${firstName?.[0] || ''}${lastName?.[0] || ''}`.toUpperCase() || 'U';
  };

  const getTimeAgo = (date: string) => {
    try {
      return formatDistanceToNow(new Date(date), { 
        addSuffix: true, 
        locale: bn 
      });
    } catch {
      return 'কিছুক্ষণ আগে';
    }
  };

  const parseImages = (images: string | string[]) => {
    if (typeof images === 'string') {
      try {
        return JSON.parse(images);
      } catch {
        return [images];
      }
    }
    return images || [];
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">লোড হচ্ছে...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated || user?.role !== "super_admin") {
    return null;
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <Shield className="h-8 w-8 text-primary" />
            <h1 className="text-3xl font-bold">সুপার অ্যাডমিন ড্যাশবোর্ড</h1>
          </div>
          <p className="text-muted-foreground">
            সম্পূর্ণ সিস্টেম ব্যবস্থাপনা এবং নিয়ন্ত্রণ
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">মোট ব্যবহারকারী</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{users?.length || 0}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">মোট পণ্য</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{productsData?.total || 0}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">সক্রিয় পণ্য</CardTitle>
              <Eye className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {productsData?.products?.filter((p: any) => p.isActive).length || 0}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="users" className="flex items-center">
              <Users className="h-4 w-4 mr-2" />
              ব্যবহারকারী ব্যবস্থাপনা
            </TabsTrigger>
            <TabsTrigger value="products" className="flex items-center">
              <Package className="h-4 w-4 mr-2" />
              পণ্য ব্যবস্থাপনা
            </TabsTrigger>
          </TabsList>

          {/* Users Tab */}
          <TabsContent value="users" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>সকল ব্যবহারকারী</CardTitle>
                <CardDescription>
                  সিস্টেমের সকল নিবন্ধিত ব্যবহারকারীর তালিকা
                </CardDescription>
              </CardHeader>
              <CardContent>
                {usersLoading ? (
                  <div className="space-y-3">
                    {Array.from({ length: 5 }).map((_, i) => (
                      <div key={i} className="flex space-x-3 animate-pulse">
                        <div className="w-10 h-10 bg-muted rounded-full"></div>
                        <div className="flex-1 space-y-2">
                          <div className="bg-muted h-4 rounded w-3/4"></div>
                          <div className="bg-muted h-3 rounded w-1/2"></div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>ব্যবহারকারী</TableHead>
                        <TableHead>যোগাযোগ</TableHead>
                        <TableHead>স্ট্যাটাস</TableHead>
                        <TableHead>যোগদান</TableHead>
                        <TableHead>কার্যক্রম</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {users?.map((userItem: any) => (
                        <TableRow key={userItem.id}>
                          <TableCell>
                            <div className="flex items-center space-x-3">
                              <Avatar className="h-10 w-10">
                                <AvatarImage 
                                  src={userItem.profileImageUrl || undefined}
                                  className="object-cover"
                                />
                                <AvatarFallback className="bg-primary text-primary-foreground text-sm">
                                  {getInitials(userItem.firstName, userItem.lastName)}
                                </AvatarFallback>
                              </Avatar>
                              <div>
                                <div className="font-medium">
                                  {userItem.firstName} {userItem.lastName}
                                </div>
                                <div className="text-sm text-muted-foreground">
                                  ID: {userItem.id}
                                </div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              {userItem.phone && (
                                <div className="flex items-center text-sm">
                                  <Phone className="h-3 w-3 mr-1" />
                                  {userItem.phone}
                                </div>
                              )}
                              {userItem.email && (
                                <div className="flex items-center text-sm">
                                  <Mail className="h-3 w-3 mr-1" />
                                  {userItem.email}
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              {userItem.isVerified && (
                                <Badge variant="secondary" className="text-xs">
                                  যাচাইকৃত
                                </Badge>
                              )}
                              {userItem.isAdmin && (
                                <Badge variant="default" className="text-xs">
                                  অ্যাডমিন
                                </Badge>
                              )}
                              {userItem.role === "super_admin" && (
                                <Badge variant="destructive" className="text-xs">
                                  সুপার অ্যাডমিন
                                </Badge>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center text-sm text-muted-foreground">
                              <Calendar className="h-3 w-3 mr-1" />
                              {getTimeAgo(userItem.createdAt)}
                            </div>
                          </TableCell>
                          <TableCell>
                            {userItem.id !== "super_admin_2025" && (
                              <AlertDialog>
                                <AlertDialogTrigger asChild>
                                  <Button variant="destructive" size="sm">
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                  <AlertDialogHeader>
                                    <AlertDialogTitle>ব্যবহারকারী মুছে ফেলুন</AlertDialogTitle>
                                    <AlertDialogDescription>
                                      আপনি কি নিশ্চিত যে আপনি এই ব্যবহারকারীকে মুছে ফেলতে চান? 
                                      এই কাজটি পূর্বাবস্থায় ফেরানো যাবে না।
                                    </AlertDialogDescription>
                                  </AlertDialogHeader>
                                  <AlertDialogFooter>
                                    <AlertDialogCancel>বাতিল</AlertDialogCancel>
                                    <AlertDialogAction
                                      onClick={() => deleteUserMutation.mutate(userItem.id)}
                                      className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                    >
                                      মুছে ফেলুন
                                    </AlertDialogAction>
                                  </AlertDialogFooter>
                                </AlertDialogContent>
                              </AlertDialog>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Products Tab */}
          <TabsContent value="products" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>সকল পণ্য</CardTitle>
                <CardDescription>
                  সিস্টেমের সকল পণ্যের তালিকা এবং ব্যবস্থাপনা
                </CardDescription>
              </CardHeader>
              <CardContent>
                {productsLoading ? (
                  <div className="space-y-3">
                    {Array.from({ length: 5 }).map((_, i) => (
                      <div key={i} className="flex space-x-3 animate-pulse">
                        <div className="w-16 h-16 bg-muted rounded"></div>
                        <div className="flex-1 space-y-2">
                          <div className="bg-muted h-4 rounded w-3/4"></div>
                          <div className="bg-muted h-3 rounded w-1/2"></div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>পণ্য</TableHead>
                        <TableHead>বিক্রেতা</TableHead>
                        <TableHead>দাম</TableHead>
                        <TableHead>অবস্থা</TableHead>
                        <TableHead>তারিখ</TableHead>
                        <TableHead>কার্যক্রম</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {productsData?.products?.map((product: any) => {
                        const productImages = parseImages(product.images);
                        const primaryImage = productImages[0] || '/placeholder.jpg';

                        return (
                          <TableRow key={product.id}>
                            <TableCell>
                              <div className="flex items-center space-x-3">
                                <img
                                  src={primaryImage}
                                  alt={product.title}
                                  className="w-16 h-16 object-cover rounded"
                                />
                                <div>
                                  <div className="font-medium line-clamp-1">
                                    {product.title}
                                  </div>
                                  <div className="text-sm text-muted-foreground">
                                    {product.category?.nameBn}
                                  </div>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center space-x-2">
                                <Avatar className="h-8 w-8">
                                  <AvatarImage
                                    src={product.seller?.profileImageUrl || undefined}
                                    className="object-cover"
                                  />
                                  <AvatarFallback className="bg-primary text-primary-foreground text-xs">
                                    {getInitials(product.seller?.firstName, product.seller?.lastName)}
                                  </AvatarFallback>
                                </Avatar>
                                <div>
                                  <div className="font-medium text-sm">
                                    {product.seller?.firstName} {product.seller?.lastName}
                                  </div>
                                  <div className="text-xs text-muted-foreground">
                                    {product.seller?.phone}
                                  </div>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="font-semibold text-secondary">
                                ৳ {product.price?.toLocaleString()}
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="space-y-1">
                                <Badge variant="outline" className="text-xs">
                                  {product.condition}
                                </Badge>
                                {!product.isActive && (
                                  <Badge variant="destructive" className="text-xs block">
                                    নিষ্ক্রিয়
                                  </Badge>
                                )}
                                {product.isSold && (
                                  <Badge variant="secondary" className="text-xs block">
                                    বিক্রিত
                                  </Badge>
                                )}
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center text-sm text-muted-foreground">
                                <Clock className="h-3 w-3 mr-1" />
                                {getTimeAgo(product.createdAt)}
                              </div>
                              <div className="flex items-center text-xs text-muted-foreground">
                                <Eye className="h-3 w-3 mr-1" />
                                {product.viewCount || 0} বার দেখা হয়েছে
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex space-x-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => window.open(`/product/${product.id}`, '_blank')}
                                >
                                  <Eye className="h-4 w-4" />
                                </Button>
                                <AlertDialog>
                                  <AlertDialogTrigger asChild>
                                    <Button variant="destructive" size="sm">
                                      <Trash2 className="h-4 w-4" />
                                    </Button>
                                  </AlertDialogTrigger>
                                  <AlertDialogContent>
                                    <AlertDialogHeader>
                                      <AlertDialogTitle>পণ্য মুছে ফেলুন</AlertDialogTitle>
                                      <AlertDialogDescription>
                                        আপনি কি নিশ্চিত যে আপনি এই পণ্যটি মুছে ফেলতে চান?
                                        এই কাজটি পূর্বাবস্থায় ফেরানো যাবে না।
                                      </AlertDialogDescription>
                                    </AlertDialogHeader>
                                    <AlertDialogFooter>
                                      <AlertDialogCancel>বাতিল</AlertDialogCancel>
                                      <AlertDialogAction
                                        onClick={() => deleteProductMutation.mutate(product.id)}
                                        className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                      >
                                        মুছে ফেলুন
                                      </AlertDialogAction>
                                    </AlertDialogFooter>
                                  </AlertDialogContent>
                                </AlertDialog>
                              </div>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
