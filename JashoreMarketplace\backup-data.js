#!/usr/bin/env node

/**
 * Data Backup and Restore Script for Jashore Marketplace
 * 
 * This script helps you backup and restore your marketplace data.
 * All data is stored in the SQLite database file (dev.db) and uploads folder.
 * 
 * Usage:
 *   node backup-data.js backup    - Creates a backup
 *   node backup-data.js restore   - Restores from backup
 */

const fs = require('fs');
const path = require('path');

const BACKUP_DIR = './backups';
const DB_FILE = './dev.db';
const UPLOADS_DIR = './uploads';

function createBackup() {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupName = `marketplace-backup-${timestamp}`;
  const backupPath = path.join(BACKUP_DIR, backupName);

  // Create backup directory
  if (!fs.existsSync(BACKUP_DIR)) {
    fs.mkdirSync(BACKUP_DIR);
  }
  fs.mkdirSync(backupPath);

  console.log(`Creating backup: ${backupName}`);

  // Copy database file
  if (fs.existsSync(DB_FILE)) {
    fs.copyFileSync(DB_FILE, path.join(backupPath, 'dev.db'));
    console.log('✅ Database backed up');
  } else {
    console.log('⚠️  Database file not found');
  }

  // Copy uploads folder
  if (fs.existsSync(UPLOADS_DIR)) {
    copyFolderSync(UPLOADS_DIR, path.join(backupPath, 'uploads'));
    console.log('✅ Uploads folder backed up');
  } else {
    console.log('⚠️  Uploads folder not found');
  }

  // Create backup info file
  const backupInfo = {
    created: new Date().toISOString(),
    version: '1.0.0',
    description: 'Jashore Marketplace Data Backup',
    files: {
      database: 'dev.db',
      uploads: 'uploads/'
    }
  };
  
  fs.writeFileSync(
    path.join(backupPath, 'backup-info.json'), 
    JSON.stringify(backupInfo, null, 2)
  );

  console.log(`\n🎉 Backup completed successfully!`);
  console.log(`📁 Backup location: ${backupPath}`);
  console.log(`\nTo transfer your marketplace to another location:`);
  console.log(`1. Copy the entire project folder`);
  console.log(`2. The dev.db file contains all your data`);
  console.log(`3. The uploads folder contains all images`);
}

function copyFolderSync(from, to) {
  if (!fs.existsSync(to)) {
    fs.mkdirSync(to, { recursive: true });
  }
  
  const files = fs.readdirSync(from);
  files.forEach(file => {
    const fromPath = path.join(from, file);
    const toPath = path.join(to, file);
    
    if (fs.statSync(fromPath).isDirectory()) {
      copyFolderSync(fromPath, toPath);
    } else {
      fs.copyFileSync(fromPath, toPath);
    }
  });
}

function listBackups() {
  if (!fs.existsSync(BACKUP_DIR)) {
    console.log('No backups found.');
    return [];
  }

  const backups = fs.readdirSync(BACKUP_DIR)
    .filter(name => name.startsWith('marketplace-backup-'))
    .sort()
    .reverse();

  if (backups.length === 0) {
    console.log('No backups found.');
    return [];
  }

  console.log('\nAvailable backups:');
  backups.forEach((backup, index) => {
    const backupPath = path.join(BACKUP_DIR, backup);
    const infoPath = path.join(backupPath, 'backup-info.json');
    
    if (fs.existsSync(infoPath)) {
      const info = JSON.parse(fs.readFileSync(infoPath, 'utf8'));
      console.log(`${index + 1}. ${backup} (${new Date(info.created).toLocaleString()})`);
    } else {
      console.log(`${index + 1}. ${backup}`);
    }
  });

  return backups;
}

function restoreBackup(backupName) {
  const backupPath = path.join(BACKUP_DIR, backupName);
  
  if (!fs.existsSync(backupPath)) {
    console.log(`❌ Backup not found: ${backupName}`);
    return;
  }

  console.log(`Restoring backup: ${backupName}`);

  // Restore database
  const backupDbPath = path.join(backupPath, 'dev.db');
  if (fs.existsSync(backupDbPath)) {
    fs.copyFileSync(backupDbPath, DB_FILE);
    console.log('✅ Database restored');
  }

  // Restore uploads
  const backupUploadsPath = path.join(backupPath, 'uploads');
  if (fs.existsSync(backupUploadsPath)) {
    if (fs.existsSync(UPLOADS_DIR)) {
      fs.rmSync(UPLOADS_DIR, { recursive: true });
    }
    copyFolderSync(backupUploadsPath, UPLOADS_DIR);
    console.log('✅ Uploads restored');
  }

  console.log('\n🎉 Restore completed successfully!');
}

// Main script
const command = process.argv[2];

switch (command) {
  case 'backup':
    createBackup();
    break;
    
  case 'restore':
    const backups = listBackups();
    if (backups.length > 0) {
      console.log('\nEnter the backup name to restore (or press Ctrl+C to cancel):');
      // For simplicity, restore the latest backup
      restoreBackup(backups[0]);
    }
    break;
    
  case 'list':
    listBackups();
    break;
    
  default:
    console.log('Jashore Marketplace Data Management');
    console.log('');
    console.log('Usage:');
    console.log('  node backup-data.js backup   - Create a backup');
    console.log('  node backup-data.js restore  - Restore latest backup');
    console.log('  node backup-data.js list     - List all backups');
    console.log('');
    console.log('Data Persistence:');
    console.log('  📁 dev.db - SQLite database (contains all data)');
    console.log('  📁 uploads/ - User uploaded images');
    console.log('  📁 node_modules/ - Dependencies (can be reinstalled)');
    console.log('');
    console.log('To transfer your marketplace:');
    console.log('  1. Copy the entire project folder');
    console.log('  2. Run "npm install" on the new location');
    console.log('  3. Run "npm run dev" to start');
    break;
}
