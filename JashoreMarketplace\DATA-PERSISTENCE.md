# Data Persistence in Jashore Marketplace

## 📊 How Your Data is Stored

Your Jashore Marketplace uses **SQLite database** for data persistence, which means all your data is stored in files that travel with your project.

### 🗃️ Data Files

| File/Folder | Contains | Portable |
|-------------|----------|----------|
| `dev.db` | All marketplace data (users, products, categories, locations, messages, etc.) | ✅ Yes |
| `uploads/` | User uploaded images and files | ✅ Yes |
| `node_modules/` | Dependencies | ❌ No (can be reinstalled) |

## 🚀 Transferring Your Marketplace

When you want to move your marketplace to another computer or server:

### Method 1: Copy Everything
```bash
# Copy the entire project folder
cp -r JashoreMarketplace /path/to/new/location

# Install dependencies
cd /path/to/new/location/JashoreMarketplace
npm install

# Start the marketplace
npm run dev
```

### Method 2: Essential Files Only
```bash
# Create new project folder
mkdir MyMarketplace
cd MyMarketplace

# Copy essential files
cp /old/location/dev.db ./
cp -r /old/location/uploads ./
cp -r /old/location/client ./
cp -r /old/location/server ./
cp -r /old/location/shared ./
cp /old/location/package.json ./
cp /old/location/package-lock.json ./
# ... copy other config files

# Install dependencies
npm install

# Start the marketplace
npm run dev
```

## 💾 Backup Your Data

### Automatic Backup Script
```bash
# Create a backup
node backup-data.js backup

# List all backups
node backup-data.js list

# Restore latest backup
node backup-data.js restore
```

### Manual Backup
```bash
# Create backup folder
mkdir marketplace-backup-$(date +%Y%m%d)

# Copy database
cp dev.db marketplace-backup-$(date +%Y%m%d)/

# Copy uploads
cp -r uploads marketplace-backup-$(date +%Y%m%d)/
```

## 🔄 Database Details

### SQLite Database (`dev.db`)
- **Type**: File-based database
- **Location**: Root directory of your project
- **Contains**: 
  - Users and authentication data
  - Products and listings
  - Categories and locations
  - Messages and conversations
  - Blog posts and favorites

### Database Tables
- `users` - User accounts and profiles
- `products` - Product listings
- `categories` - Product categories
- `locations` - Geographic locations
- `conversations` - Chat conversations
- `messages` - Chat messages
- `blog_posts` - Blog articles
- `favorites` - User favorites

## 🛡️ Data Safety Tips

1. **Regular Backups**: Use the backup script weekly
2. **Version Control**: Keep your code in Git (exclude `dev.db` and `uploads/` for privacy)
3. **Multiple Copies**: Keep backups in different locations
4. **Test Restores**: Periodically test your backup restoration process

## 🔧 Troubleshooting

### Database Issues
```bash
# Check if database exists
ls -la dev.db

# Check database size
du -h dev.db

# Backup before fixing
cp dev.db dev.db.backup
```

### Missing Uploads
```bash
# Check uploads folder
ls -la uploads/

# Restore uploads from backup
cp -r backup/uploads ./
```

## 📈 Scaling Considerations

### For Production Use
- Consider migrating to PostgreSQL for better performance
- Use cloud storage for uploads (AWS S3, etc.)
- Implement automated backups
- Use environment variables for configuration

### Current Setup Benefits
- ✅ **Simple**: No database server required
- ✅ **Portable**: Everything in files
- ✅ **Fast**: SQLite is very fast for small to medium datasets
- ✅ **Reliable**: SQLite is battle-tested and stable

## 🎯 Summary

Your Jashore Marketplace data is **100% portable** and stored in:
- `dev.db` (database file)
- `uploads/` (images folder)

Simply copy these files with your project, run `npm install`, and your marketplace will work exactly the same on any computer! 🚀
