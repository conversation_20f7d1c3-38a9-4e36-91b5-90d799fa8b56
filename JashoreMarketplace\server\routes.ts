import type { Express } from "express";
import express from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { setupAuth as setupReplitAuth, isAuthenticated as isReplitAuthenticated } from "./replitAuth";
import { setupAuth as setupLocalAuth, isAuthenticated as isLocalAuthenticated } from "./localAuth";
import { insertProductSchema, insertMessageSchema, insertBlogPostSchema, insertCategorySchema, insertLocationSchema } from "@shared/schema-sqlite";
import { z } from "zod";
import multer from "multer";
import path from "path";
import fs from "fs";
import authRoutes from "./routes/auth";

// Helper function to parse images from JSON string to array
function parseProductImages(product: any) {
  if (product.images && typeof product.images === 'string') {
    try {
      product.images = JSON.parse(product.images);
    } catch (error) {
      console.error('Error parsing product images:', error);
      product.images = [];
    }
  }
  return product;
}

// Configure multer for file uploads
const uploadDir = path.join(process.cwd(), "uploads");
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

const upload = multer({
  dest: uploadDir,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  },
});

export async function registerRoutes(app: Express): Promise<Server> {
  // Auth middleware - use local auth in development, Replit auth in production
  const isLocalDev = process.env.NODE_ENV === 'development' && process.env.DATABASE_URL?.startsWith('file:');
  const setupAuth = isLocalDev ? setupLocalAuth : setupReplitAuth;
  const isAuthenticated = isLocalDev ? isLocalAuthenticated : isReplitAuthenticated;

  await setupAuth(app);

  // Serve uploaded files
  app.use('/uploads', express.static(uploadDir));

  // Custom auth routes (login/register)
  app.use('/api/auth', authRoutes);

  // Auth routes
  app.get('/api/auth/user', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const user = await storage.getUser(userId);
      
      // Update last seen
      await storage.updateUserStatus(userId, new Date());
      
      res.json(user);
    } catch (error) {
      console.error("Error fetching user:", error);
      res.status(500).json({ message: "Failed to fetch user" });
    }
  });

  // Categories routes
  app.get('/api/categories', async (req, res) => {
    try {
      const categories = await storage.getCategories();
      res.json(categories);
    } catch (error) {
      console.error("Error fetching categories:", error);
      res.status(500).json({ message: "Failed to fetch categories" });
    }
  });

  app.post('/api/categories', isAuthenticated, async (req: any, res) => {
    try {
      const user = await storage.getUser(req.user.claims.sub);
      if (!user?.isAdmin) {
        return res.status(403).json({ message: "Admin access required" });
      }

      const categoryData = insertCategorySchema.parse(req.body);
      const category = await storage.createCategory(categoryData);
      res.json(category);
    } catch (error) {
      console.error("Error creating category:", error);
      res.status(500).json({ message: "Failed to create category" });
    }
  });

  // Locations routes
  app.get('/api/locations', async (req, res) => {
    try {
      const locations = await storage.getLocations();
      res.json(locations);
    } catch (error) {
      console.error("Error fetching locations:", error);
      res.status(500).json({ message: "Failed to fetch locations" });
    }
  });

  app.post('/api/locations', isAuthenticated, async (req: any, res) => {
    try {
      const user = await storage.getUser(req.user.claims.sub);
      if (!user?.isAdmin) {
        return res.status(403).json({ message: "Admin access required" });
      }

      const locationData = insertLocationSchema.parse(req.body);
      const location = await storage.createLocation(locationData);
      res.json(location);
    } catch (error) {
      console.error("Error creating location:", error);
      res.status(500).json({ message: "Failed to create location" });
    }
  });

  // Products routes
  app.get('/api/products', async (req, res) => {
    try {
      const {
        categoryId,
        locationId,
        minPrice,
        maxPrice,
        condition,
        search,
        page = '1',
        limit = '20'
      } = req.query;

      const offset = (parseInt(page as string) - 1) * parseInt(limit as string);

      const filters = {
        categoryId: categoryId ? parseInt(categoryId as string) : undefined,
        locationId: locationId ? parseInt(locationId as string) : undefined,
        minPrice: minPrice ? parseFloat(minPrice as string) : undefined,
        maxPrice: maxPrice ? parseFloat(maxPrice as string) : undefined,
        condition: condition as string,
        search: search as string,
        limit: parseInt(limit as string),
        offset,
      };

      const result = await storage.getProducts(filters);

      // Parse images for all products
      const productsWithParsedImages = {
        ...result,
        products: result.products.map(parseProductImages)
      };

      res.json(productsWithParsedImages);
    } catch (error) {
      console.error("Error fetching products:", error);
      res.status(500).json({ message: "Failed to fetch products" });
    }
  });

  app.get('/api/products/:id', async (req, res) => {
    try {
      const product = await storage.getProduct(req.params.id);
      if (!product) {
        return res.status(404).json({ message: "Product not found" });
      }

      // Increment view count
      await storage.incrementProductViews(req.params.id);

      // Parse images
      const productWithParsedImages = parseProductImages(product);

      res.json(productWithParsedImages);
    } catch (error) {
      console.error("Error fetching product:", error);
      res.status(500).json({ message: "Failed to fetch product" });
    }
  });

  app.post('/api/products', isAuthenticated, upload.array('images', 10), async (req: any, res) => {
    try {
      const productData = insertProductSchema.parse({
        ...req.body,
        price: parseFloat(req.body.price),
        categoryId: parseInt(req.body.categoryId),
        locationId: parseInt(req.body.locationId),
      });

      // Handle uploaded images
      const images = req.files ? req.files.map((file: any) => `/uploads/${file.filename}`) : [];

      const finalProductData = { ...productData, images };

      const product = await storage.createProduct(
        finalProductData,
        req.user.claims.sub
      );

      // Parse images before sending response
      const productWithParsedImages = parseProductImages(product);

      res.json(productWithParsedImages);
    } catch (error) {
      console.error("Error creating product:", error);
      res.status(500).json({ message: "Failed to create product" });
    }
  });

  app.put('/api/products/:id', isAuthenticated, async (req: any, res) => {
    try {
      const product = await storage.getProduct(req.params.id);
      if (!product) {
        return res.status(404).json({ message: "Product not found" });
      }

      if (product.sellerId !== req.user.claims.sub) {
        return res.status(403).json({ message: "Not authorized to update this product" });
      }

      const updates = insertProductSchema.partial().parse(req.body);
      const updatedProduct = await storage.updateProduct(req.params.id, updates);
      res.json(updatedProduct);
    } catch (error) {
      console.error("Error updating product:", error);
      res.status(500).json({ message: "Failed to update product" });
    }
  });

  app.delete('/api/products/:id', isAuthenticated, async (req: any, res) => {
    try {
      const product = await storage.getProduct(req.params.id);
      if (!product) {
        return res.status(404).json({ message: "Product not found" });
      }

      if (product.sellerId !== req.user.claims.sub) {
        return res.status(403).json({ message: "Not authorized to delete this product" });
      }

      await storage.deleteProduct(req.params.id);
      res.json({ message: "Product deleted successfully" });
    } catch (error) {
      console.error("Error deleting product:", error);
      res.status(500).json({ message: "Failed to delete product" });
    }
  });

  // User products
  app.get('/api/users/:userId/products', async (req, res) => {
    try {
      const result = await storage.getProducts({ sellerId: req.params.userId });

      // Parse images for all products
      const productsWithParsedImages = {
        ...result,
        products: result.products.map(parseProductImages)
      };

      res.json(productsWithParsedImages);
    } catch (error) {
      console.error("Error fetching user products:", error);
      res.status(500).json({ message: "Failed to fetch user products" });
    }
  });

  // Conversations routes
  app.get('/api/conversations', isAuthenticated, async (req: any, res) => {
    try {
      const conversations = await storage.getUserConversations(req.user.claims.sub);
      res.json(conversations);
    } catch (error) {
      console.error("Error fetching conversations:", error);
      res.status(500).json({ message: "Failed to fetch conversations" });
    }
  });

  app.get('/api/conversations/:id', isAuthenticated, async (req: any, res) => {
    try {
      const conversation = await storage.getConversation(req.params.id);
      if (!conversation) {
        return res.status(404).json({ message: "Conversation not found" });
      }

      const userId = req.user.claims.sub;
      if (conversation.buyerId !== userId && conversation.sellerId !== userId) {
        return res.status(403).json({ message: "Not authorized to view this conversation" });
      }

      res.json(conversation);
    } catch (error) {
      console.error("Error fetching conversation:", error);
      res.status(500).json({ message: "Failed to fetch conversation" });
    }
  });

  app.post('/api/conversations', isAuthenticated, async (req: any, res) => {
    try {
      const { productId, sellerId } = req.body;
      const buyerId = req.user.claims.sub;

      // Check if conversation already exists
      const existing = await storage.findConversation(productId, buyerId, sellerId);
      if (existing) {
        return res.json(existing);
      }

      const conversation = await storage.createConversation({
        id: crypto.randomUUID(),
        productId,
        buyerId,
        sellerId,
      });

      res.json(conversation);
    } catch (error) {
      console.error("Error creating conversation:", error);
      res.status(500).json({ message: "Failed to create conversation" });
    }
  });

  // Messages routes
  app.get('/api/conversations/:id/messages', isAuthenticated, async (req: any, res) => {
    try {
      const conversation = await storage.getConversation(req.params.id);
      if (!conversation) {
        return res.status(404).json({ message: "Conversation not found" });
      }

      const userId = req.user.claims.sub;
      if (conversation.buyerId !== userId && conversation.sellerId !== userId) {
        return res.status(403).json({ message: "Not authorized to view these messages" });
      }

      const messages = await storage.getMessages(req.params.id);
      
      // Mark messages as read for the current user
      await storage.markMessagesAsRead(req.params.id, userId);

      res.json(messages);
    } catch (error) {
      console.error("Error fetching messages:", error);
      res.status(500).json({ message: "Failed to fetch messages" });
    }
  });

  app.post('/api/conversations/:id/messages', isAuthenticated, upload.single('image'), async (req: any, res) => {
    try {
      const conversation = await storage.getConversation(req.params.id);
      if (!conversation) {
        return res.status(404).json({ message: "Conversation not found" });
      }

      const userId = req.user.claims.sub;
      if (conversation.buyerId !== userId && conversation.sellerId !== userId) {
        return res.status(403).json({ message: "Not authorized to send messages in this conversation" });
      }

      let messageData: any = {
        conversationId: req.params.id,
        senderId: userId,
      };

      if (req.file) {
        messageData.imageUrl = `/uploads/${req.file.filename}`;
        messageData.messageType = 'image';
      } else {
        messageData.content = req.body.content;
        messageData.messageType = 'text';
      }

      const validatedData = insertMessageSchema.parse(messageData);
      const message = await storage.createMessage(validatedData);

      res.json(message);
    } catch (error) {
      console.error("Error creating message:", error);
      res.status(500).json({ message: "Failed to create message" });
    }
  });

  // Unread messages count
  app.get('/api/messages/unread-count', isAuthenticated, async (req: any, res) => {
    try {
      const count = await storage.getUnreadMessageCount(req.user.claims.sub);
      res.json({ count });
    } catch (error) {
      console.error("Error fetching unread count:", error);
      res.status(500).json({ message: "Failed to fetch unread count" });
    }
  });

  // Blog routes
  app.get('/api/blog', async (req, res) => {
    try {
      const posts = await storage.getBlogPosts(true); // Only published posts
      res.json(posts);
    } catch (error) {
      console.error("Error fetching blog posts:", error);
      res.status(500).json({ message: "Failed to fetch blog posts" });
    }
  });

  app.get('/api/blog/:id', async (req, res) => {
    try {
      const post = await storage.getBlogPost(req.params.id);
      if (!post) {
        return res.status(404).json({ message: "Blog post not found" });
      }
      res.json(post);
    } catch (error) {
      console.error("Error fetching blog post:", error);
      res.status(500).json({ message: "Failed to fetch blog post" });
    }
  });

  app.post('/api/blog', isAuthenticated, upload.single('featuredImage'), async (req: any, res) => {
    try {
      const user = await storage.getUser(req.user.claims.sub);
      if (!user?.isAdmin) {
        return res.status(403).json({ message: "Admin access required" });
      }

      const postData = insertBlogPostSchema.parse({
        ...req.body,
        featuredImage: req.file ? `/uploads/${req.file.filename}` : undefined,
      });

      const post = await storage.createBlogPost(postData, req.user.claims.sub);
      res.json(post);
    } catch (error) {
      console.error("Error creating blog post:", error);
      res.status(500).json({ message: "Failed to create blog post" });
    }
  });

  app.put('/api/blog/:id', isAuthenticated, async (req: any, res) => {
    try {
      const user = await storage.getUser(req.user.claims.sub);
      if (!user?.isAdmin) {
        return res.status(403).json({ message: "Admin access required" });
      }

      const updates = insertBlogPostSchema.partial().parse(req.body);
      const post = await storage.updateBlogPost(req.params.id, updates);
      res.json(post);
    } catch (error) {
      console.error("Error updating blog post:", error);
      res.status(500).json({ message: "Failed to update blog post" });
    }
  });

  app.post('/api/blog/:id/publish', isAuthenticated, async (req: any, res) => {
    try {
      const user = await storage.getUser(req.user.claims.sub);
      if (!user?.isAdmin) {
        return res.status(403).json({ message: "Admin access required" });
      }

      const post = await storage.publishBlogPost(req.params.id);
      res.json(post);
    } catch (error) {
      console.error("Error publishing blog post:", error);
      res.status(500).json({ message: "Failed to publish blog post" });
    }
  });

  // Helper function to check super admin access
  const requireSuperAdmin = async (req: any, res: any, next: any) => {
    try {
      const user = await storage.getUser(req.user.claims.sub);
      if (!user?.isAdmin && req.user.claims.sub !== "super_admin_2025") {
        return res.status(403).json({ message: "Super admin access required" });
      }
      next();
    } catch (error) {
      return res.status(500).json({ message: "Authorization check failed" });
    }
  };

  // Super Admin routes
  app.get('/api/super-admin/users', isAuthenticated, requireSuperAdmin, async (req: any, res) => {
    try {
      const users = await storage.getAllUsers();
      res.json(users);
    } catch (error) {
      console.error("Error fetching users:", error);
      res.status(500).json({ message: "Failed to fetch users" });
    }
  });

  app.get('/api/super-admin/products', isAuthenticated, requireSuperAdmin, async (req: any, res) => {
    try {
      const result = await storage.getProducts({ limit: 1000 }); // Get all products
      res.json(result);
    } catch (error) {
      console.error("Error fetching all products:", error);
      res.status(500).json({ message: "Failed to fetch products" });
    }
  });

  app.delete('/api/super-admin/products/:id', isAuthenticated, requireSuperAdmin, async (req: any, res) => {
    try {
      await storage.deleteProduct(req.params.id);
      res.json({ message: "Product deleted successfully" });
    } catch (error) {
      console.error("Error deleting product:", error);
      res.status(500).json({ message: "Failed to delete product" });
    }
  });

  app.delete('/api/super-admin/users/:id', isAuthenticated, requireSuperAdmin, async (req: any, res) => {
    try {
      // Don't allow deleting super admin
      if (req.params.id === "super_admin_2025") {
        return res.status(403).json({ message: "Cannot delete super admin" });
      }

      await storage.deleteUser(req.params.id);
      res.json({ message: "User deleted successfully" });
    } catch (error) {
      console.error("Error deleting user:", error);
      res.status(500).json({ message: "Failed to delete user" });
    }
  });

  // Admin routes (existing)
  app.get('/api/admin/users', isAuthenticated, async (req: any, res) => {
    try {
      const user = await storage.getUser(req.user.claims.sub);
      if (!user?.isAdmin && req.user.claims.sub !== "super_admin_2025") {
        return res.status(403).json({ message: "Admin access required" });
      }

      const users = await storage.getAllUsers();
      res.json(users);
    } catch (error) {
      console.error("Error fetching users:", error);
      res.status(500).json({ message: "Failed to fetch users" });
    }
  });

  app.post('/api/admin/users/:id/verify', isAuthenticated, async (req: any, res) => {
    try {
      const user = await storage.getUser(req.user.claims.sub);
      if (!user?.isAdmin) {
        return res.status(403).json({ message: "Admin access required" });
      }

      await storage.verifyUser(req.params.id);
      res.json({ message: "User verified successfully" });
    } catch (error) {
      console.error("Error verifying user:", error);
      res.status(500).json({ message: "Failed to verify user" });
    }
  });

  app.post('/api/admin/users/:id/make-admin', isAuthenticated, async (req: any, res) => {
    try {
      const user = await storage.getUser(req.user.claims.sub);
      if (!user?.isAdmin) {
        return res.status(403).json({ message: "Admin access required" });
      }

      await storage.makeAdmin(req.params.id);
      res.json({ message: "User promoted to admin successfully" });
    } catch (error) {
      console.error("Error promoting user:", error);
      res.status(500).json({ message: "Failed to promote user" });
    }
  });

  app.get('/api/admin/blog', isAuthenticated, async (req: any, res) => {
    try {
      const user = await storage.getUser(req.user.claims.sub);
      if (!user?.isAdmin) {
        return res.status(403).json({ message: "Admin access required" });
      }

      const posts = await storage.getBlogPosts(); // All posts including unpublished
      res.json(posts);
    } catch (error) {
      console.error("Error fetching admin blog posts:", error);
      res.status(500).json({ message: "Failed to fetch blog posts" });
    }
  });

  // Favorites routes
  app.get('/api/favorites', isAuthenticated, async (req: any, res) => {
    try {
      const favorites = await storage.getFavorites(req.user.claims.sub);
      res.json(favorites);
    } catch (error) {
      console.error("Error fetching favorites:", error);
      res.status(500).json({ message: "Failed to fetch favorites" });
    }
  });

  app.post('/api/favorites/:productId', isAuthenticated, async (req: any, res) => {
    try {
      const favorite = await storage.addToFavorites(req.user.claims.sub, req.params.productId);
      res.json(favorite);
    } catch (error) {
      console.error("Error adding to favorites:", error);
      res.status(500).json({ message: "Failed to add to favorites" });
    }
  });

  app.delete('/api/favorites/:productId', isAuthenticated, async (req: any, res) => {
    try {
      await storage.removeFromFavorites(req.user.claims.sub, req.params.productId);
      res.json({ message: "Removed from favorites" });
    } catch (error) {
      console.error("Error removing from favorites:", error);
      res.status(500).json({ message: "Failed to remove from favorites" });
    }
  });

  app.get('/api/favorites/:productId/check', isAuthenticated, async (req: any, res) => {
    try {
      const isFavorite = await storage.isFavorite(req.user.claims.sub, req.params.productId);
      res.json({ isFavorite });
    } catch (error) {
      console.error("Error checking favorite status:", error);
      res.status(500).json({ message: "Failed to check favorite status" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
