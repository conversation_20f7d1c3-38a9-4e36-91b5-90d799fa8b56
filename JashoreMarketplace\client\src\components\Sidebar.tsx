import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Link } from "wouter";
import { DollarSign, MapPin, Filter, Calendar } from "lucide-react";

interface SidebarProps {
  onFiltersChange: (filters: any) => void;
  filters: any;
}

export default function Sidebar({ onFiltersChange, filters }: SidebarProps) {
  const [localFilters, setLocalFilters] = useState({
    minPrice: filters.minPrice || '',
    maxPrice: filters.maxPrice || '',
    condition: filters.condition || 'all',
    locationId: filters.locationId || 'all',
    verifiedOnly: false,
  });

  const { data: locations } = useQuery({
    queryKey: ['/api/locations'],
  });

  const { data: recentBlogs } = useQuery({
    queryKey: ['/api/blog'],
  });

  const handleFilterChange = (key: string, value: any) => {
    const newFilters = { ...localFilters, [key]: value };
    setLocalFilters(newFilters);

    // Apply filters immediately for better UX
    const filtersToApply: any = {};
    if (newFilters.minPrice) filtersToApply.minPrice = parseFloat(newFilters.minPrice);
    if (newFilters.maxPrice) filtersToApply.maxPrice = parseFloat(newFilters.maxPrice);
    if (newFilters.condition && newFilters.condition !== "all") filtersToApply.condition = newFilters.condition;
    if (newFilters.locationId && newFilters.locationId !== "all") filtersToApply.locationId = parseInt(newFilters.locationId);

    onFiltersChange(filtersToApply);
  };

  const clearFilters = () => {
    const clearedFilters = {
      minPrice: '',
      maxPrice: '',
      condition: 'all',
      locationId: 'all',
      verifiedOnly: false,
    };
    setLocalFilters(clearedFilters);
    onFiltersChange({});
  };

  const hasActiveFilters = localFilters.minPrice || localFilters.maxPrice ||
                          (localFilters.condition && localFilters.condition !== "all") ||
                          (localFilters.locationId && localFilters.locationId !== "all") ||
                          localFilters.verifiedOnly;

  return (
    <aside className="space-y-6">
      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center">
              <Filter className="h-5 w-5 mr-2" />
              ফিল্টার
            </div>
            {hasActiveFilters && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFilters}
                className="text-muted-foreground hover:text-foreground"
              >
                সাফ করুন
              </Button>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Price Filter */}
          <div>
            <Label className="text-sm font-medium text-foreground mb-2 flex items-center">
              <DollarSign className="h-4 w-4 mr-1" />
              মূল্য সীমা
            </Label>
            <div className="flex space-x-2">
              <Input
                type="number"
                placeholder="সর্বনিম্ন"
                value={localFilters.minPrice}
                onChange={(e) => handleFilterChange('minPrice', e.target.value)}
                className="text-sm"
              />
              <Input
                type="number"
                placeholder="সর্বোচ্চ"
                value={localFilters.maxPrice}
                onChange={(e) => handleFilterChange('maxPrice', e.target.value)}
                className="text-sm"
              />
            </div>
          </div>

          {/* Condition Filter */}
          <div>
            <Label className="text-sm font-medium text-foreground mb-2 block">
              অবস্থা
            </Label>
            <Select
              value={localFilters.condition}
              onValueChange={(value) => handleFilterChange('condition', value)}
            >
              <SelectTrigger className="text-sm">
                <SelectValue placeholder="অবস্থা নির্বাচন করুন" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">সব অবস্থা</SelectItem>
                <SelectItem value="new">নতুন</SelectItem>
                <SelectItem value="used">ব্যবহৃত</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Location Filter */}
          <div>
            <Label className="text-sm font-medium text-foreground mb-2 flex items-center">
              <MapPin className="h-4 w-4 mr-1" />
              এলাকা
            </Label>
            <Select
              value={localFilters.locationId}
              onValueChange={(value) => handleFilterChange('locationId', value)}
            >
              <SelectTrigger className="text-sm">
                <SelectValue placeholder="এলাকা নির্বাচন করুন" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">সব এলাকা</SelectItem>
                {locations?.map((location: any) => (
                  <SelectItem key={location.id} value={location.id.toString()}>
                    {location.areaBn}, {location.districtBn}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Verified Seller Filter */}
          <div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="verifiedOnly"
                checked={localFilters.verifiedOnly}
                onCheckedChange={(checked) => handleFilterChange('verifiedOnly', checked)}
              />
              <Label
                htmlFor="verifiedOnly"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                শুধু যাচাইকৃত বিক্রেতা
              </Label>
            </div>
          </div>

          {/* Active Filters Display */}
          {hasActiveFilters && (
            <div>
              <Label className="text-sm font-medium text-foreground mb-2 block">
                সক্রিয় ফিল্টার
              </Label>
              <div className="flex flex-wrap gap-2">
                {localFilters.minPrice && (
                  <Badge variant="secondary" className="text-xs">
                    মিন: ৳{localFilters.minPrice}
                  </Badge>
                )}
                {localFilters.maxPrice && (
                  <Badge variant="secondary" className="text-xs">
                    ম্যাক্স: ৳{localFilters.maxPrice}
                  </Badge>
                )}
                {localFilters.condition && localFilters.condition !== "all" && (
                  <Badge variant="secondary" className="text-xs">
                    {localFilters.condition === 'new' ? 'নতুন' : 'ব্যবহৃত'}
                  </Badge>
                )}
                {localFilters.locationId && localFilters.locationId !== "all" && (
                  <Badge variant="secondary" className="text-xs">
                    {locations?.find((l: any) => l.id.toString() === localFilters.locationId)?.areaBn}
                  </Badge>
                )}
                {localFilters.verifiedOnly && (
                  <Badge variant="secondary" className="text-xs">
                    যাচাইকৃত
                  </Badge>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recent Blog Posts */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="h-5 w-5 mr-2" />
            সাম্প্রতিক ব্লগ
          </CardTitle>
        </CardHeader>
        <CardContent>
          {recentBlogs && recentBlogs.length > 0 ? (
            <div className="space-y-4">
              {recentBlogs.slice(0, 3).map((blog: any) => (
                <Link key={blog.id} href={`/blog/${blog.id}`}>
                  <div className="cursor-pointer group">
                    {blog.featuredImage && (
                      <img
                        src={blog.featuredImage}
                        alt={blog.title}
                        className="w-full h-20 object-cover rounded mb-2 group-hover:opacity-80 transition-opacity"
                      />
                    )}
                    <h4 className="text-sm font-medium text-foreground mb-1 group-hover:text-primary transition-colors line-clamp-2">
                      {blog.title}
                    </h4>
                    <p className="text-xs text-muted-foreground">
                      {blog.publishedAt ? 
                        new Date(blog.publishedAt).toLocaleDateString('bn-BD') :
                        new Date(blog.createdAt).toLocaleDateString('bn-BD')
                      }
                    </p>
                  </div>
                </Link>
              ))}
              <Link href="/blog">
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full text-primary border-primary hover:bg-primary hover:text-primary-foreground"
                >
                  সব ব্লগ দেখুন
                </Button>
              </Link>
            </div>
          ) : (
            <div className="text-center py-6">
              <div className="text-4xl mb-2">📝</div>
              <p className="text-sm text-muted-foreground">
                কোন ব্লগ পোস্ট নেই
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Stats */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">দ্রুত তথ্য</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 text-sm">
            <div className="flex items-center justify-between">
              <span className="text-muted-foreground">মোট পণ্য</span>
              <Badge variant="outline">১০,০০০+</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-muted-foreground">সক্রিয় বিক্রেতা</span>
              <Badge variant="outline">৫,০০০+</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-muted-foreground">যাচাইকৃত বিক্রেতা</span>
              <Badge variant="outline" className="bg-success/10 text-success border-success">
                ১,৫০০+
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Safety Tips */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm text-warning">নিরাপত্তার জন্য</CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="text-xs text-muted-foreground space-y-2">
            <li className="flex items-start space-x-2">
              <span className="text-warning mt-0.5">•</span>
              <span>পণ্য কেনার আগে সরাসরি দেখে নিন</span>
            </li>
            <li className="flex items-start space-x-2">
              <span className="text-warning mt-0.5">•</span>
              <span>নিরাপদ স্থানে লেনদেন করুন</span>
            </li>
            <li className="flex items-start space-x-2">
              <span className="text-warning mt-0.5">•</span>
              <span>সন্দেহজনক অফার এড়িয়ে চলুন</span>
            </li>
            <li className="flex items-start space-x-2">
              <span className="text-warning mt-0.5">•</span>
              <span>অগ্রিম টাকা পাঠাবেন না</span>
            </li>
            <li className="flex items-start space-x-2">
              <span className="text-warning mt-0.5">•</span>
              <span>যাচাইকৃত বিক্রেতা বেছে নিন</span>
            </li>
          </ul>
        </CardContent>
      </Card>
    </aside>
  );
}
