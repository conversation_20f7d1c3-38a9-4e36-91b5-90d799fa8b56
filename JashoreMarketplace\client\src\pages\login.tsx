import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { useMutation } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  User,
  Shield,
  MessageSquare,
  Plus,
  ArrowRight,
  CheckCircle,
  Info,
  Phone,
  Mail,
  Lock,
  Eye,
  EyeOff
} from "lucide-react";

export default function Login() {
  const [, setLocation] = useLocation();
  const { isAuthenticated } = useAuth();
  const { toast } = useToast();

  // Form states
  const [showPassword, setShowPassword] = useState(false);
  const [loginForm, setLoginForm] = useState({
    phoneOrEmail: '',
    password: ''
  });
  const [registerForm, setRegisterForm] = useState({
    firstName: '',
    lastName: '',
    phone: '',
    email: '',
    password: '',
    confirmPassword: ''
  });

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      setLocation("/home");
    }
  }, [isAuthenticated, setLocation]);

  // Login mutation
  const loginMutation = useMutation({
    mutationFn: async (data: { phoneOrEmail: string; password: string }) => {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Login failed');
      }

      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "সফলভাবে লগইন হয়েছে",
        description: "আপনি সফলভাবে লগইন হয়েছেন",
      });
      setLocation("/home");
    },
    onError: (error: Error) => {
      toast({
        title: "লগইন ব্যর্থ",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  // Register mutation
  const registerMutation = useMutation({
    mutationFn: async (data: typeof registerForm) => {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Registration failed');
      }

      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "সফলভাবে রেজিস্ট্রেশন হয়েছে",
        description: "আপনি সফলভাবে রেজিস্ট্রেশন করেছেন এবং লগইন হয়েছেন",
      });
      setLocation("/home");
    },
    onError: (error: Error) => {
      toast({
        title: "রেজিস্ট্রেশন ব্যর্থ",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();
    if (!loginForm.phoneOrEmail || !loginForm.password) {
      toast({
        title: "তথ্য অসম্পূর্ণ",
        description: "দয়া করে সব ক্ষেত্র পূরণ করুন",
        variant: "destructive",
      });
      return;
    }
    loginMutation.mutate(loginForm);
  };

  const handleRegister = (e: React.FormEvent) => {
    e.preventDefault();
    if (!registerForm.firstName || !registerForm.phone || !registerForm.password) {
      toast({
        title: "তথ্য অসম্পূর্ণ",
        description: "দয়া করে সব প্রয়োজনীয় ক্ষেত্র পূরণ করুন",
        variant: "destructive",
      });
      return;
    }
    if (registerForm.password !== registerForm.confirmPassword) {
      toast({
        title: "পাসওয়ার্ড মিলছে না",
        description: "পাসওয়ার্ড এবং নিশ্চিত পাসওয়ার্ড একই হতে হবে",
        variant: "destructive",
      });
      return;
    }
    registerMutation.mutate(registerForm);
  };

  const handleBackToHome = () => {
    setLocation("/home");
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-muted/10 to-background flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-6">
        {/* Logo/Brand */}
        <div className="text-center space-y-2">
          <div className="w-16 h-16 mx-auto premium-gradient rounded-2xl flex items-center justify-center">
            <div className="text-2xl">🛒</div>
          </div>
          <h1 className="text-2xl font-bold text-foreground">Jashore Marketplace</h1>
          <p className="text-muted-foreground">যশোর মার্কেটপ্লেস</p>
        </div>

        {/* Auth Forms */}
        <Card className="glass-effect border-0 luxury-shadow">
          <Tabs defaultValue="login" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="login">লগইন</TabsTrigger>
              <TabsTrigger value="register">রেজিস্ট্রেশন</TabsTrigger>
            </TabsList>

            {/* Login Tab */}
            <TabsContent value="login">
              <CardHeader className="text-center">
                <CardTitle className="text-xl">লগইন করুন</CardTitle>
                <CardDescription>
                  আপনার ফোন নম্বর বা ইমেইল এবং পাসওয়ার্ড দিয়ে লগইন করুন
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleLogin} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="phoneOrEmail">ফোন নম্বর বা ইমেইল *</Label>
                    <div className="relative">
                      <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="phoneOrEmail"
                        type="text"
                        placeholder="01XXXXXXXXX বা <EMAIL>"
                        value={loginForm.phoneOrEmail}
                        onChange={(e) => setLoginForm(prev => ({ ...prev, phoneOrEmail: e.target.value }))}
                        className="pl-10"
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="password">পাসওয়ার্ড *</Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="password"
                        type={showPassword ? "text" : "password"}
                        placeholder="আপনার পাসওয়ার্ড"
                        value={loginForm.password}
                        onChange={(e) => setLoginForm(prev => ({ ...prev, password: e.target.value }))}
                        className="pl-10 pr-10"
                        required
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? (
                          <EyeOff className="h-4 w-4 text-muted-foreground" />
                        ) : (
                          <Eye className="h-4 w-4 text-muted-foreground" />
                        )}
                      </Button>
                    </div>
                  </div>

                  <Button
                    type="submit"
                    className="w-full premium-gradient text-white rounded-xl premium-hover luxury-shadow"
                    size="lg"
                    disabled={loginMutation.isPending}
                  >
                    {loginMutation.isPending ? (
                      "লগইন হচ্ছে..."
                    ) : (
                      <>
                        <User className="mr-2 h-5 w-5" />
                        লগইন করুন
                      </>
                    )}
                  </Button>
                </form>
              </CardContent>
            </TabsContent>

            {/* Register Tab */}
            <TabsContent value="register">
              <CardHeader className="text-center">
                <CardTitle className="text-xl">নতুন অ্যাকাউন্ট</CardTitle>
                <CardDescription>
                  নতুন অ্যাকাউন্ট তৈরি করুন এবং মার্কেটপ্লেসে যোগ দিন
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleRegister} className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="firstName">নাম *</Label>
                      <Input
                        id="firstName"
                        type="text"
                        placeholder="আপনার নাম"
                        value={registerForm.firstName}
                        onChange={(e) => setRegisterForm(prev => ({ ...prev, firstName: e.target.value }))}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="lastName">পদবি</Label>
                      <Input
                        id="lastName"
                        type="text"
                        placeholder="পদবি"
                        value={registerForm.lastName}
                        onChange={(e) => setRegisterForm(prev => ({ ...prev, lastName: e.target.value }))}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone">ফোন নম্বর *</Label>
                    <div className="relative">
                      <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="phone"
                        type="tel"
                        placeholder="01XXXXXXXXX"
                        value={registerForm.phone}
                        onChange={(e) => setRegisterForm(prev => ({ ...prev, phone: e.target.value }))}
                        className="pl-10"
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email">ইমেইল (ঐচ্ছিক)</Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="email"
                        type="email"
                        placeholder="<EMAIL>"
                        value={registerForm.email}
                        onChange={(e) => setRegisterForm(prev => ({ ...prev, email: e.target.value }))}
                        className="pl-10"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="registerPassword">পাসওয়ার্ড *</Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="registerPassword"
                        type={showPassword ? "text" : "password"}
                        placeholder="নিরাপদ পাসওয়ার্ড"
                        value={registerForm.password}
                        onChange={(e) => setRegisterForm(prev => ({ ...prev, password: e.target.value }))}
                        className="pl-10 pr-10"
                        required
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? (
                          <EyeOff className="h-4 w-4 text-muted-foreground" />
                        ) : (
                          <Eye className="h-4 w-4 text-muted-foreground" />
                        )}
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword">পাসওয়ার্ড নিশ্চিত করুন *</Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="confirmPassword"
                        type={showPassword ? "text" : "password"}
                        placeholder="পাসওয়ার্ড আবার লিখুন"
                        value={registerForm.confirmPassword}
                        onChange={(e) => setRegisterForm(prev => ({ ...prev, confirmPassword: e.target.value }))}
                        className="pl-10"
                        required
                      />
                    </div>
                  </div>

                  <Button
                    type="submit"
                    className="w-full premium-gradient text-white rounded-xl premium-hover luxury-shadow"
                    size="lg"
                    disabled={registerMutation.isPending}
                  >
                    {registerMutation.isPending ? (
                      "রেজিস্ট্রেশন হচ্ছে..."
                    ) : (
                      <>
                        <User className="mr-2 h-5 w-5" />
                        রেজিস্ট্রেশন করুন
                      </>
                    )}
                  </Button>
                </form>
              </CardContent>
            </TabsContent>
          </Tabs>
        </Card>

        {/* Back to browsing */}
        <div className="text-center">
          <Button
            variant="ghost"
            onClick={handleBackToHome}
            className="text-muted-foreground hover:text-foreground"
          >
            লগইন ছাড়াই ব্রাউজ করুন
          </Button>
        </div>

        {/* Additional Info */}
        <div className="text-center space-y-2">
          <Badge variant="outline" className="text-xs">
            🔒 নিরাপদ ও সুরক্ষিত
          </Badge>
          <p className="text-xs text-muted-foreground">
            আপনার তথ্য সম্পূর্ণ নিরাপদ এবং গোপনীয়
          </p>
        </div>
      </div>
    </div>
  );
}
