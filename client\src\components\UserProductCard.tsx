import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { 
  Edit, 
  Trash2, 
  Eye, 
  MapPin, 
  Calendar,
  Phone,
  Heart,
  MessageCircle
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { bn } from "date-fns/locale";
import { api } from "@/lib/api";

interface UserProductCardProps {
  product: any;
  viewMode: 'grid' | 'list';
  onEdit: (product: any) => void;
}

export default function UserProductCard({ product, viewMode, onEdit }: UserProductCardProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [imageError, setImageError] = useState(false);

  const deleteProductMutation = useMutation({
    mutationFn: (productId: string) => api.deleteProduct(productId),
    onSuccess: () => {
      toast({
        title: "সফল",
        description: "পণ্য সফলভাবে মুছে ফেলা হয়েছে",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/users'] });
    },
    onError: () => {
      toast({
        title: "ত্রুটি",
        description: "পণ্য মুছে ফেলতে পারিনি",
        variant: "destructive",
      });
    },
  });

  const parseImages = (images: string | string[]) => {
    if (typeof images === 'string') {
      try {
        return JSON.parse(images);
      } catch {
        return [images];
      }
    }
    return images || [];
  };

  const getTimeAgo = (date: string) => {
    try {
      return formatDistanceToNow(new Date(date), { 
        addSuffix: true, 
        locale: bn 
      });
    } catch {
      return 'কিছুক্ষণ আগে';
    }
  };

  const handleImageError = () => {
    setImageError(true);
  };

  const productImages = parseImages(product.images);
  const primaryImage = productImages[0] || '/placeholder.jpg';

  const handleProductClick = () => {
    window.open(`/product/${product.id}`, '_blank');
  };

  if (viewMode === 'list') {
    return (
      <Card className="hover:shadow-md transition-shadow">
        <CardContent className="p-4">
          <div className="flex space-x-4">
            <div className="w-32 h-24 flex-shrink-0">
              <img
                src={imageError ? '/placeholder.jpg' : primaryImage}
                alt={product.title}
                className="w-full h-full object-cover rounded cursor-pointer"
                onError={handleImageError}
                onClick={handleProductClick}
              />
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex justify-between items-start mb-2">
                <h3 
                  className="font-semibold text-lg cursor-pointer hover:text-primary line-clamp-1"
                  onClick={handleProductClick}
                >
                  {product.title}
                </h3>
                <div className="flex space-x-2 ml-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onEdit(product)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant="destructive" size="sm">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>পণ্য মুছে ফেলুন</AlertDialogTitle>
                        <AlertDialogDescription>
                          আপনি কি নিশ্চিত যে আপনি এই পণ্যটি মুছে ফেলতে চান? 
                          এই কাজটি পূর্বাবস্থায় ফেরানো যাবে না।
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>বাতিল</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={() => deleteProductMutation.mutate(product.id)}
                          className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        >
                          মুছে ফেলুন
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </div>
              
              <p className="text-2xl font-bold text-secondary mb-2">
                ৳ {product.price?.toLocaleString()}
              </p>
              
              <div className="flex items-center space-x-4 text-sm text-muted-foreground mb-2">
                <div className="flex items-center">
                  <MapPin className="h-4 w-4 mr-1" />
                  {product.location?.districtBn}
                </div>
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-1" />
                  {getTimeAgo(product.createdAt)}
                </div>
                <div className="flex items-center">
                  <Eye className="h-4 w-4 mr-1" />
                  {product.viewCount || 0} বার দেখা হয়েছে
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex space-x-2">
                  <Badge variant="secondary">{product.condition}</Badge>
                  <Badge variant="outline">{product.category?.nameBn}</Badge>
                  {!product.isActive && (
                    <Badge variant="destructive">নিষ্ক্রিয়</Badge>
                  )}
                  {product.isSold && (
                    <Badge variant="secondary">বিক্রিত</Badge>
                  )}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="group hover:shadow-lg transition-all duration-300 overflow-hidden">
      <div className="relative">
        <div className="aspect-square overflow-hidden">
          <img
            src={imageError ? '/placeholder.jpg' : primaryImage}
            alt={product.title}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300 cursor-pointer"
            onError={handleImageError}
            onClick={handleProductClick}
          />
        </div>
        
        {productImages.length > 1 && (
          <Badge className="absolute top-2 left-2 bg-black/70 text-white">
            +{productImages.length - 1}
          </Badge>
        )}
        
        <div className="absolute top-2 right-2 flex space-x-1">
          <Button
            variant="secondary"
            size="sm"
            onClick={() => onEdit(product)}
            className="bg-white/90 hover:bg-white"
          >
            <Edit className="h-4 w-4" />
          </Button>
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button 
                variant="destructive" 
                size="sm"
                className="bg-red-500/90 hover:bg-red-500"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>পণ্য মুছে ফেলুন</AlertDialogTitle>
                <AlertDialogDescription>
                  আপনি কি নিশ্চিত যে আপনি এই পণ্যটি মুছে ফেলতে চান? 
                  এই কাজটি পূর্বাবস্থায় ফেরানো যাবে না।
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>বাতিল</AlertDialogCancel>
                <AlertDialogAction
                  onClick={() => deleteProductMutation.mutate(product.id)}
                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                >
                  মুছে ফেলুন
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>
      
      <CardContent className="p-4">
        <h3 
          className="font-semibold text-lg mb-2 line-clamp-2 cursor-pointer hover:text-primary"
          onClick={handleProductClick}
        >
          {product.title}
        </h3>
        
        <p className="text-2xl font-bold text-secondary mb-3">
          ৳ {product.price?.toLocaleString()}
        </p>
        
        <div className="flex items-center justify-between text-sm text-muted-foreground mb-3">
          <div className="flex items-center">
            <MapPin className="h-4 w-4 mr-1" />
            {product.location?.districtBn}
          </div>
          <div className="flex items-center">
            <Calendar className="h-4 w-4 mr-1" />
            {getTimeAgo(product.createdAt)}
          </div>
        </div>
        
        <div className="flex items-center justify-between text-sm text-muted-foreground mb-3">
          <div className="flex items-center">
            <Eye className="h-4 w-4 mr-1" />
            {product.viewCount || 0}
          </div>
          <Badge variant="outline" className="text-xs">
            {product.category?.nameBn}
          </Badge>
        </div>
        
        <div className="flex flex-wrap gap-1 mb-3">
          <Badge variant="secondary" className="text-xs">{product.condition}</Badge>
          {!product.isActive && (
            <Badge variant="destructive" className="text-xs">নিষ্ক্রিয়</Badge>
          )}
          {product.isSold && (
            <Badge variant="secondary" className="text-xs">বিক্রিত</Badge>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
