import { defineConfig } from "drizzle-kit";

if (!process.env.DATABASE_URL) {
  throw new Error("DATABASE_URL, ensure the database is provisioned");
}

// Determine database type from URL
const isSqlite = process.env.DATABASE_URL.startsWith('file:');
const isPostgres = process.env.DATABASE_URL.startsWith('postgresql://') || process.env.DATABASE_URL.startsWith('postgres://');

let config;

if (isSqlite) {
  config = defineConfig({
    out: "./migrations",
    schema: "./shared/schema-sqlite.ts",
    dialect: "sqlite",
    dbCredentials: {
      url: process.env.DATABASE_URL.replace('file:', ''),
    },
  });
} else if (isPostgres) {
  config = defineConfig({
    out: "./migrations",
    schema: "./shared/schema.ts",
    dialect: "postgresql",
    dbCredentials: {
      url: process.env.DATABASE_URL,
    },
  });
} else {
  throw new Error("Unsupported database URL format. Use postgresql:// or file:// protocol");
}

export default config;
