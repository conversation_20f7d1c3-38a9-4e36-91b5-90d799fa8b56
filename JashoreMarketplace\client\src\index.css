@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Premium Trust-Building Color Palette */
  --background: 210 20% 98%; /* #FAFBFC - Ultra-light blue-gray */
  --foreground: 210 30% 8%; /* #0F1419 - Deep navy */
  --muted: 210 15% 94%; /* #F1F3F4 - Soft gray */
  --muted-foreground: 210 10% 40%; /* #6B7280 - Medium gray */
  --popover: 0 0% 100%; /* Pure white */
  --popover-foreground: 210 30% 8%;
  --card: 0 0% 100%; /* Pure white */
  --card-foreground: 210 30% 8%;
  --border: 210 15% 88%; /* #E5E7EB - Light border */
  --input: 210 15% 94%; /* #F9FAFB - Input background */
  --primary: 220 85% 57%; /* #3B82F6 - Trust blue */
  --primary-foreground: 0 0% 100%;
  --secondary: 262 80% 60%; /* #8B5CF6 - Premium purple */
  --secondary-foreground: 0 0% 100%;
  --accent: 32 95% 60%; /* #F59E0B - Luxury gold */
  --accent-foreground: 210 30% 8%;
  --destructive: 354 70% 54%; /* #EF4444 - Error red */
  --destructive-foreground: 0 0% 100%;
  --ring: 220 85% 57%;
  --radius: 0.75rem;
  --success: 142 76% 36%; /* #059669 - Success green */
  --warning: 38 92% 50%; /* #F59E0B - Warning amber */
  --luxury: 262 80% 60%; /* #8B5CF6 - Luxury accent */
  --trust: 220 85% 57%; /* #3B82F6 - Trust builder */
}

.dark {
  --background: 210 30% 8%; /* #0F1419 - Deep dark navy */
  --foreground: 210 20% 98%;
  --muted: 210 20% 15%; /* #1F2937 - Dark muted */
  --muted-foreground: 210 10% 60%; /* #9CA3AF - Light gray */
  --popover: 210 30% 10%; /* #111827 - Dark popover */
  --popover-foreground: 210 20% 98%;
  --card: 210 30% 10%; /* #111827 - Dark card */
  --card-foreground: 210 20% 98%;
  --border: 210 20% 20%; /* #374151 - Dark border */
  --input: 210 20% 15%; /* #1F2937 - Dark input */
  --primary: 220 85% 65%; /* #60A5FA - Brighter blue for dark mode */
  --primary-foreground: 210 30% 8%;
  --secondary: 262 80% 70%; /* #A78BFA - Brighter purple */
  --secondary-foreground: 210 30% 8%;
  --accent: 32 95% 65%; /* #FBBF24 - Brighter gold */
  --accent-foreground: 210 30% 8%;
  --destructive: 354 70% 60%; /* #F87171 - Brighter red */
  --destructive-foreground: 210 20% 98%;
  --ring: 220 85% 65%;
  --success: 142 76% 45%; /* #10B981 - Brighter green */
  --warning: 38 92% 60%; /* #FBBF24 - Brighter amber */
  --luxury: 262 80% 70%; /* #A78BFA - Brighter luxury */
  --trust: 220 85% 65%; /* #60A5FA - Brighter trust */
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  }
}

@layer utilities {
  .text-success {
    color: hsl(var(--success));
  }
  
  .bg-success {
    background-color: hsl(var(--success));
  }
  
  .text-warning {
    color: hsl(var(--warning));
  }
  
  .bg-warning {
    background-color: hsl(var(--warning));
  }

  .border-success {
    border-color: hsl(var(--success));
  }

  .border-warning {
    border-color: hsl(var(--warning));
  }
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground));
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary));
}

/* Premium Animations & Effects */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes float {
  0%, 100% { 
    transform: translateY(0px); 
  }
  50% { 
    transform: translateY(-10px); 
  }
}

.float-animation {
  animation: float 6s ease-in-out infinite;
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.gradient-animate {
  background-size: 200% 200%;
  animation: gradient-shift 4s ease infinite;
}

.glass-effect {
  backdrop-filter: blur(16px);
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.dark .glass-effect {
  background: rgba(15, 20, 25, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.premium-gradient {
  background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--luxury)) 50%, hsl(var(--accent)) 100%);
}

.trust-gradient {
  background: linear-gradient(135deg, hsl(var(--trust)) 0%, hsl(var(--primary)) 100%);
}

.luxury-shadow {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1);
}

.premium-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.premium-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}
