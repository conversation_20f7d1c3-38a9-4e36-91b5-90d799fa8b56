import { useState, useEffect } from "react";
import { useParams, useLocation } from "wouter";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { isUnauthorizedError } from "@/lib/authUtils";
import { useToast } from "@/hooks/use-toast";
import { api } from "@/lib/api";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import ChatModal from "@/components/ChatModal";
import RelatedProducts from "@/components/RelatedProducts";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { 
  Heart, 
  MapPin, 
  Phone, 
  Shield, 
  Eye, 
  Calendar, 
  MessageSquare,
  Share2,
  Flag,
  ChevronLeft,
  ChevronRight
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { bn } from "date-fns/locale";
import { hideMobileNumbers } from "@/utils/textUtils";

export default function ProductDetails() {
  const { id } = useParams();
  const [, setLocation] = useLocation();
  const { user, isAuthenticated } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isChatOpen, setIsChatOpen] = useState(false);

  const { data: product, isLoading, error } = useQuery({
    queryKey: [`/api/products/${id}`],
    enabled: !!id,
  });

  const { data: favoriteStatus } = useQuery({
    queryKey: [`/api/favorites/${id}/check`],
    enabled: !!id && isAuthenticated,
  });

  const { data: sellerProducts } = useQuery({
    queryKey: [`/api/users/${product?.sellerId}/products`],
    enabled: !!product?.sellerId,
  });

  const toggleFavoriteMutation = useMutation({
    mutationFn: async () => {
      if (favoriteStatus?.isFavorite) {
        await api.removeFromFavorites(id!);
      } else {
        await api.addToFavorites(id!);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/favorites/${id}/check`] });
      toast({
        title: favoriteStatus?.isFavorite ? "পছন্দের তালিকা থেকে সরানো হয়েছে" : "পছন্দের তালিকায় যোগ করা হয়েছে",
        duration: 2000,
      });
    },
    onError: (error) => {
      if (isUnauthorizedError(error as Error)) {
        toast({
          title: "অনুমতি নেই",
          description: "আপনি লগআউট হয়ে গেছেন। আবার লগইন করুন...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/login";
        }, 500);
        return;
      }
      toast({
        title: "ত্রুটি",
        description: "দয়া করে আবার চেষ্টা করুন",
        variant: "destructive",
      });
    },
  });

  useEffect(() => {
    if (!isLoading && !product && error) {
      if (isUnauthorizedError(error as Error)) {
        toast({
          title: "অনুমতি নেই",
          description: "আপনি লগআউট হয়ে গেছেন। আবার লগইন করুন...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/login";
        }, 500);
        return;
      }
      setLocation("/");
    }
  }, [isLoading, product, error, setLocation, toast]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <Header 
          onOpenChat={() => setIsChatOpen(true)}
          onOpenPostModal={() => {}}
          onSearch={() => {}}
          onLocationChange={() => {}}
        />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="animate-pulse">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
              <div className="bg-muted h-96 rounded-lg"></div>
              <div className="space-y-4">
                <div className="bg-muted h-8 rounded w-3/4"></div>
                <div className="bg-muted h-6 rounded w-1/2"></div>
                <div className="bg-muted h-20 rounded"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="min-h-screen bg-background">
        <Header 
          onOpenChat={() => setIsChatOpen(true)}
          onOpenPostModal={() => {}}
          onSearch={() => {}}
          onLocationChange={() => {}}
        />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="text-center py-12">
            <h1 className="text-2xl font-bold text-foreground mb-4">পণ্য পাওয়া যায়নি</h1>
            <Button onClick={() => setLocation("/")}>হোমে ফিরে যান</Button>
          </div>
        </div>
      </div>
    );
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('bn-BD', {
      style: 'currency',
      currency: 'BDT',
      minimumFractionDigits: 0,
    }).format(price).replace('BDT', '৳');
  };

  const getConditionBadge = (condition: string) => {
    if (condition === 'new') {
      return <Badge className="bg-success text-white">নতুন</Badge>;
    }
    return <Badge variant="secondary">ব্যবহৃত</Badge>;
  };

  const getTimeAgo = (date: string) => {
    try {
      return formatDistanceToNow(new Date(date), { 
        addSuffix: true, 
        locale: bn 
      });
    } catch {
      return 'কিছুক্ষণ আগে';
    }
  };

  const isOnline = (lastSeen?: string) => {
    if (!lastSeen) return false;
    const lastSeenDate = new Date(lastSeen);
    const now = new Date();
    const diffInMinutes = (now.getTime() - lastSeenDate.getTime()) / (1000 * 60);
    return diffInMinutes < 5;
  };

  const getInitials = (firstName?: string, lastName?: string) => {
    return `${firstName?.[0] || ''}${lastName?.[0] || ''}`.toUpperCase() || 'U';
  };

  const images = product.images || [];
  const currentImage = images[currentImageIndex];

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % images.length);
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + images.length) % images.length);
  };

  const handleStartChat = () => {
    if (!isAuthenticated) {
      toast({
        title: "লগইন প্রয়োজন",
        description: "চ্যাট করতে প্রথমে লগইন করুন",
        variant: "destructive",
      });
      return;
    }
    setIsChatOpen(true);
  };

  return (
    <div className="min-h-screen bg-background">
      <Header 
        onOpenChat={() => setIsChatOpen(true)}
        onOpenPostModal={() => {}}
        onSearch={() => {}}
        onLocationChange={() => {}}
      />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Breadcrumb */}
        <nav className="text-sm text-muted-foreground mb-6">
          <span 
            className="hover:text-primary cursor-pointer"
            onClick={() => setLocation("/")}
          >
            হোম
          </span>
          <span className="mx-2">/</span>
          <span>{product.category?.nameBn}</span>
          <span className="mx-2">/</span>
          <span className="text-foreground">{hideMobileNumbers(product.title)}</span>
        </nav>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 lg:gap-8">
          {/* Image Gallery */}
          <div className="lg:col-span-2">
            <Card>
              <CardContent className="p-0">
                <div className="relative">
                  {currentImage ? (
                    <div className="w-full h-96 bg-muted rounded-t-lg flex items-center justify-center overflow-hidden">
                      <img
                        src={currentImage}
                        alt={product.title}
                        className="max-w-full max-h-full object-contain"
                      />
                    </div>
                  ) : (
                    <div className="w-full h-96 bg-muted rounded-t-lg flex items-center justify-center">
                      <span className="text-muted-foreground">ছবি নেই</span>
                    </div>
                  )}
                  
                  {images.length > 1 && (
                    <>
                      <Button
                        variant="outline"
                        size="sm"
                        className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-white/80"
                        onClick={prevImage}
                      >
                        <ChevronLeft className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-white/80"
                        onClick={nextImage}
                      >
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 bg-black/50 text-white px-2 py-1 rounded text-xs">
                        {currentImageIndex + 1} / {images.length}
                      </div>
                    </>
                  )}

                  <div className="absolute top-2 left-2">
                    {getConditionBadge(product.condition)}
                  </div>

                  <div className="absolute top-2 right-2 flex space-x-2">
                    {isAuthenticated && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="bg-white/80"
                        onClick={() => toggleFavoriteMutation.mutate()}
                        disabled={toggleFavoriteMutation.isPending}
                      >
                        <Heart 
                          className={`h-4 w-4 ${favoriteStatus?.isFavorite ? 'fill-red-500 text-red-500' : ''}`}
                        />
                      </Button>
                    )}
                    <Button variant="outline" size="sm" className="bg-white/80">
                      <Share2 className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="sm" className="bg-white/80">
                      <Flag className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* Thumbnail Gallery */}
                {images.length > 1 && (
                  <div className="p-4">
                    <div className="grid grid-cols-4 sm:grid-cols-6 gap-2">
                      {images.map((image, index) => (
                        <button
                          key={index}
                          className={`relative aspect-square rounded border-2 overflow-hidden ${
                            index === currentImageIndex ? 'border-primary' : 'border-transparent'
                          }`}
                          onClick={() => setCurrentImageIndex(index)}
                        >
                          <img
                            src={image}
                            alt={`${product.title} ${index + 1}`}
                            className="w-full h-full object-cover"
                          />
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Product Description */}
            <Card className="mt-6">
              <CardContent className="p-6">
                <h2 className="text-xl font-semibold mb-4">বিবরণ</h2>
                <div className="whitespace-pre-wrap text-muted-foreground">
                  {hideMobileNumbers(product.description)}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Product Info Sidebar */}
          <div className="space-y-6">
            {/* Price and Title */}
            <Card>
              <CardContent className="p-6">
                <h1 className="text-2xl font-bold mb-2">{hideMobileNumbers(product.title)}</h1>
                <p className="text-3xl font-bold text-secondary mb-4">
                  {formatPrice(parseFloat(product.price))}
                </p>

                <div className="flex items-center space-x-4 text-sm text-muted-foreground mb-4">
                  <div className="flex items-center">
                    <MapPin className="h-4 w-4 mr-1" />
                    <span>{product.location?.areaBn}, {product.location?.districtBn}</span>
                  </div>
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-1" />
                    <span>{getTimeAgo(product.createdAt)}</span>
                  </div>
                </div>

                <div className="flex items-center space-x-4 text-sm text-muted-foreground mb-6">
                  <div className="flex items-center">
                    <Eye className="h-4 w-4 mr-1" />
                    <span>{product.viewCount || 0} বার দেখা হয়েছে</span>
                  </div>
                </div>

                {isAuthenticated && user?.id !== product.sellerId ? (
                  <div className="space-y-3">
                    <Button 
                      onClick={handleStartChat}
                      className="w-full bg-primary text-primary-foreground hover:bg-primary/90"
                    >
                      <MessageSquare className="h-4 w-4 mr-2" />
                      বিক্রেতার সাথে চ্যাট করুন
                    </Button>
                    {product.contactPhone && (
                      <Button variant="outline" className="w-full">
                        <Phone className="h-4 w-4 mr-2" />
                        ফোন করুন
                      </Button>
                    )}
                  </div>
                ) : !isAuthenticated ? (
                  <Button 
                    onClick={() => window.location.href = '/api/login'}
                    className="w-full bg-primary text-primary-foreground hover:bg-primary/90"
                  >
                    লগইন করে যোগাযোগ করুন
                  </Button>
                ) : (
                  <div className="text-center text-muted-foreground py-4">
                    এটি আপনার নিজের বিজ্ঞাপন
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Seller Info */}
            <Card>
              <CardContent className="p-6">
                <h3 className="font-semibold mb-4">বিক্রেতার তথ্য</h3>
                <div className="flex items-center space-x-3 mb-4">
                  <Avatar className="h-12 w-12">
                    <AvatarImage 
                      src={product.seller?.profileImageUrl || undefined}
                      className="object-cover"
                    />
                    <AvatarFallback className="bg-primary text-primary-foreground">
                      {getInitials(product.seller?.firstName, product.seller?.lastName)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="flex items-center">
                      <h4 className="font-medium mr-2">
                        {product.seller?.firstName || 'নাম নেই'}
                      </h4>
                      {product.seller?.isVerified && (
                        <Shield className="h-4 w-4 text-success" title="যাচাইকৃত বিক্রেতা" />
                      )}
                    </div>
                    <div className="flex items-center text-sm text-muted-foreground">
                      <div className={`w-2 h-2 rounded-full mr-2 ${
                        isOnline(product.seller?.lastSeen) ? 'bg-success pulse' : 'bg-muted-foreground'
                      }`} />
                      <span>
                        {isOnline(product.seller?.lastSeen) ? 'অনলাইন' : 'অফলাইন'}
                      </span>
                    </div>
                  </div>
                </div>

                {sellerProducts && sellerProducts.products?.length > 0 && (
                  <div>
                    <Separator className="my-4" />
                    <p className="text-sm text-muted-foreground mb-3">
                      এই বিক্রেতার মোট {sellerProducts.products.length} টি বিজ্ঞাপন রয়েছে
                    </p>
                    <Button variant="outline" size="sm" className="w-full">
                      আরও বিজ্ঞাপন দেখুন
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Safety Tips */}
            <Card>
              <CardContent className="p-6">
                <h3 className="font-semibold mb-4 text-warning">নিরাপত্তার জন্য</h3>
                <ul className="text-sm text-muted-foreground space-y-2">
                  <li>• পণ্য কেনার আগে সরাসরি দেখে নিন</li>
                  <li>• নিরাপদ স্থানে লেনদেন করুন</li>
                  <li>• সন্দেহজনক অফার এড়িয়ে চলুন</li>
                  <li>• অগ্রিম টাকা পাঠাবেন না</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Related Products */}
        <div className="mt-12">
          <h2 className="text-2xl font-bold mb-6">সম্পর্কিত পণ্য</h2>
          <RelatedProducts
            categoryId={product.categoryId}
            currentProductId={product.id}
            onProductClick={(productId) => setLocation(`/product/${productId}`)}
          />
        </div>
      </main>

      <Footer />

      <ChatModal
        isOpen={isChatOpen}
        onClose={() => setIsChatOpen(false)}
        productId={product.id}
      />
    </div>
  );
}
