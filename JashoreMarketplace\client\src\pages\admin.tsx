import { useState, useEffect } from "react";
import { useAuth } from "@/hooks/useAuth";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { isUnauthorizedError } from "@/lib/authUtils";
import { useToast } from "@/hooks/use-toast";
import { api } from "@/lib/api";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import RichTextEditor from "@/components/RichTextEditor";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { 
  Users, 
  FileText, 
  Shield, 
  Settings, 
  Plus,
  Edit,
  Trash2,
  Eye,
  CheckCircle,
  XCircle,
  Calendar,
  Mail,
  Phone
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { bn } from "date-fns/locale";

export default function Admin() {
  const { user, isAuthenticated, isLoading } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [selectedTab, setSelectedTab] = useState("users");
  const [isCreatePostOpen, setIsCreatePostOpen] = useState(false);
  const [editingPost, setEditingPost] = useState<any>(null);
  
  const [blogForm, setBlogForm] = useState({
    title: '',
    content: '',
    excerpt: '',
    featuredImage: null as File | null,
  });

  useEffect(() => {
    if (!isLoading && (!isAuthenticated || !user?.isAdmin)) {
      toast({
        title: "অনুমতি নেই",
        description: "আপনার অ্যাডমিন অ্যাক্সেস নেই",
        variant: "destructive",
      });
      setTimeout(() => {
        window.location.href = "/";
      }, 500);
      return;
    }
  }, [isAuthenticated, isLoading, user, toast]);

  const { data: users, isLoading: usersLoading } = useQuery({
    queryKey: ['/api/admin/users'],
    enabled: isAuthenticated && user?.isAdmin,
  });

  const { data: blogPosts, isLoading: blogLoading } = useQuery({
    queryKey: ['/api/admin/blog'],
    enabled: isAuthenticated && user?.isAdmin,
  });

  const verifyUserMutation = useMutation({
    mutationFn: (userId: string) => api.verifyUser(userId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/users'] });
      toast({
        title: "ব্যবহারকারী যাচাই করা হয়েছে",
        duration: 2000,
      });
    },
    onError: (error) => {
      if (isUnauthorizedError(error as Error)) {
        toast({
          title: "অনুমতি নেই",
          description: "আপনি লগআউট হয়ে গেছেন। আবার লগইন করুন...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/login";
        }, 500);
        return;
      }
      toast({
        title: "ত্রুটি",
        description: "ব্যবহারকারী যাচাই করতে পারিনি",
        variant: "destructive",
      });
    },
  });

  const makeAdminMutation = useMutation({
    mutationFn: (userId: string) => api.makeAdmin(userId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/users'] });
      toast({
        title: "অ্যাডমিন বানানো হয়েছে",
        duration: 2000,
      });
    },
    onError: (error) => {
      if (isUnauthorizedError(error as Error)) {
        toast({
          title: "অনুমতি নেই",
          description: "আপনি লগআউট হয়ে গেছেন। আবার লগইন করুন...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/login";
        }, 500);
        return;
      }
      toast({
        title: "ত্রুটি",
        description: "অ্যাডমিন বানাতে পারিনি",
        variant: "destructive",
      });
    },
  });

  const createBlogPostMutation = useMutation({
    mutationFn: (data: typeof blogForm) => api.createBlogPost(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/blog'] });
      setBlogForm({ title: '', content: '', excerpt: '', featuredImage: null });
      setIsCreatePostOpen(false);
      toast({
        title: "ব্লগ পোস্ট তৈরি করা হয়েছে",
        duration: 2000,
      });
    },
    onError: (error) => {
      if (isUnauthorizedError(error as Error)) {
        toast({
          title: "অনুমতি নেই",
          description: "আপনি লগআউট হয়ে গেছেন। আবার লগইন করুন...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "ত্রুটি",
        description: "ব্লগ পোস্ট তৈরি করতে পারিনি",
        variant: "destructive",
      });
    },
  });

  const publishBlogPostMutation = useMutation({
    mutationFn: (postId: string) => api.publishBlogPost(postId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/blog'] });
      toast({
        title: "ব্লগ পোস্ট প্রকাশ করা হয়েছে",
        duration: 2000,
      });
    },
    onError: (error) => {
      if (isUnauthorizedError(error as Error)) {
        toast({
          title: "অনুমতি নেই",
          description: "আপনি লগআউট হয়ে গেছেন। আবার লগইন করুন...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "ত্রুটি",
        description: "ব্লগ পোস্ট প্রকাশ করতে পারিনি",
        variant: "destructive",
      });
    },
  });

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <Header 
          onOpenChat={() => {}}
          onOpenPostModal={() => {}}
          onSearch={() => {}}
          onLocationChange={() => {}}
        />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="animate-pulse space-y-6">
            <div className="bg-muted h-12 rounded-lg"></div>
            <div className="bg-muted h-96 rounded-lg"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!isAuthenticated || !user?.isAdmin) {
    return null;
  }

  const getInitials = (firstName?: string, lastName?: string) => {
    return `${firstName?.[0] || ''}${lastName?.[0] || ''}`.toUpperCase() || 'U';
  };

  const getTimeAgo = (date?: string) => {
    if (!date) return 'তারিখ নেই';
    try {
      return formatDistanceToNow(new Date(date), { 
        addSuffix: true, 
        locale: bn 
      });
    } catch {
      return 'কিছুক্ষণ আগে';
    }
  };

  const isOnline = (lastSeen?: string) => {
    if (!lastSeen) return false;
    const lastSeenDate = new Date(lastSeen);
    const now = new Date();
    const diffInMinutes = (now.getTime() - lastSeenDate.getTime()) / (1000 * 60);
    return diffInMinutes < 5;
  };

  const handleCreateBlogPost = () => {
    if (!blogForm.title.trim() || !blogForm.content.trim()) {
      toast({
        title: "ত্রুটি",
        description: "শিরোনাম এবং কন্টেন্ট পূরণ করুন",
        variant: "destructive",
      });
      return;
    }
    createBlogPostMutation.mutate(blogForm);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setBlogForm(prev => ({ ...prev, featuredImage: file }));
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header 
        onOpenChat={() => {}}
        onOpenPostModal={() => {}}
        onSearch={() => {}}
        onLocationChange={() => {}}
      />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Admin Header */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-6 w-6 mr-2" />
              অ্যাডমিন প্যানেল
            </CardTitle>
          </CardHeader>
        </Card>

        {/* Admin Content */}
        <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="users" className="flex items-center">
              <Users className="h-4 w-4 mr-2" />
              ব্যবহারকারী ব্যবস্থাপনা
            </TabsTrigger>
            <TabsTrigger value="blog" className="flex items-center">
              <FileText className="h-4 w-4 mr-2" />
              ব্লগ ব্যবস্থাপনা
            </TabsTrigger>
          </TabsList>

          <TabsContent value="users">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Users className="h-5 w-5 mr-2" />
                    ব্যবহারকারী তালিকা
                  </div>
                  <Badge variant="secondary">
                    মোট: {users?.length || 0}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {usersLoading ? (
                  <div className="space-y-4">
                    {Array.from({ length: 5 }).map((_, i) => (
                      <div key={i} className="bg-muted h-16 rounded animate-pulse"></div>
                    ))}
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>ব্যবহারকারী</TableHead>
                        <TableHead>যোগাযোগ</TableHead>
                        <TableHead>স্ট্যাটাস</TableHead>
                        <TableHead>যোগদান</TableHead>
                        <TableHead>কার্যক্রম</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {users?.map((userItem: any) => (
                        <TableRow key={userItem.id}>
                          <TableCell>
                            <div className="flex items-center space-x-3">
                              <div className="relative">
                                <Avatar className="h-10 w-10">
                                  <AvatarImage 
                                    src={userItem.profileImageUrl || undefined}
                                    className="object-cover"
                                  />
                                  <AvatarFallback className="bg-primary text-primary-foreground text-sm">
                                    {getInitials(userItem.firstName, userItem.lastName)}
                                  </AvatarFallback>
                                </Avatar>
                                <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-background ${
                                  isOnline(userItem.lastSeen) ? 'bg-success' : 'bg-muted-foreground'
                                }`} />
                              </div>
                              <div>
                                <div className="font-medium">
                                  {userItem.firstName} {userItem.lastName}
                                </div>
                                <div className="flex items-center space-x-1">
                                  {userItem.isVerified && (
                                    <Badge className="bg-success text-white text-xs">
                                      যাচাইকৃত
                                    </Badge>
                                  )}
                                  {userItem.isAdmin && (
                                    <Badge variant="secondary" className="text-xs">
                                      অ্যাডমিন
                                    </Badge>
                                  )}
                                </div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              {userItem.email && (
                                <div className="flex items-center mb-1">
                                  <Mail className="h-3 w-3 mr-1" />
                                  {userItem.email}
                                </div>
                              )}
                              {userItem.phone && (
                                <div className="flex items-center">
                                  <Phone className="h-3 w-3 mr-1" />
                                  {userItem.phone}
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-1">
                              <div className={`w-2 h-2 rounded-full ${
                                isOnline(userItem.lastSeen) ? 'bg-success' : 'bg-muted-foreground'
                              }`} />
                              <span className="text-sm">
                                {isOnline(userItem.lastSeen) ? 'অনলাইন' : 'অফলাইন'}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center text-sm text-muted-foreground">
                              <Calendar className="h-3 w-3 mr-1" />
                              {getTimeAgo(userItem.createdAt)}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex space-x-2">
                              {!userItem.isVerified && (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => verifyUserMutation.mutate(userItem.id)}
                                  disabled={verifyUserMutation.isPending}
                                  className="text-success border-success hover:bg-success hover:text-white"
                                >
                                  <CheckCircle className="h-3 w-3 mr-1" />
                                  যাচাই করুন
                                </Button>
                              )}
                              {!userItem.isAdmin && (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => makeAdminMutation.mutate(userItem.id)}
                                  disabled={makeAdminMutation.isPending}
                                  className="text-primary border-primary hover:bg-primary hover:text-white"
                                >
                                  <Shield className="h-3 w-3 mr-1" />
                                  অ্যাডমিন
                                </Button>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="blog">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center">
                    <FileText className="h-5 w-5 mr-2" />
                    ব্লগ পোস্ট
                  </div>
                  <Dialog open={isCreatePostOpen} onOpenChange={setIsCreatePostOpen}>
                    <DialogTrigger asChild>
                      <Button className="bg-secondary text-secondary-foreground hover:bg-secondary/90">
                        <Plus className="h-4 w-4 mr-2" />
                        নতুন পোস্ট
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle>নতুন ব্লগ পোস্ট তৈরি করুন</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="title">শিরোনাম</Label>
                          <Input
                            id="title"
                            value={blogForm.title}
                            onChange={(e) => setBlogForm(prev => ({ ...prev, title: e.target.value }))}
                            placeholder="ব্লগ পোস্টের শিরোনাম"
                          />
                        </div>
                        
                        <div>
                          <Label htmlFor="excerpt">সংক্ষিপ্ত বিবরণ</Label>
                          <Textarea
                            id="excerpt"
                            value={blogForm.excerpt}
                            onChange={(e) => setBlogForm(prev => ({ ...prev, excerpt: e.target.value }))}
                            placeholder="পোস্টের সংক্ষিপ্ত বিবরণ"
                            rows={3}
                          />
                        </div>

                        <div>
                          <Label htmlFor="featuredImage">ফিচার ইমেজ</Label>
                          <Input
                            id="featuredImage"
                            type="file"
                            accept="image/*"
                            onChange={handleFileChange}
                          />
                        </div>

                        <div>
                          <Label>কন্টেন্ট</Label>
                          <RichTextEditor
                            content={blogForm.content}
                            onChange={(content) => setBlogForm(prev => ({ ...prev, content }))}
                          />
                        </div>

                        <div className="flex space-x-2">
                          <Button
                            onClick={handleCreateBlogPost}
                            disabled={createBlogPostMutation.isPending}
                            className="bg-primary text-primary-foreground hover:bg-primary/90"
                          >
                            {createBlogPostMutation.isPending ? 'তৈরি করা হচ্ছে...' : 'পোস্ট তৈরি করুন'}
                          </Button>
                          <Button
                            variant="outline"
                            onClick={() => setIsCreatePostOpen(false)}
                          >
                            বাতিল
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {blogLoading ? (
                  <div className="space-y-4">
                    {Array.from({ length: 3 }).map((_, i) => (
                      <div key={i} className="bg-muted h-24 rounded animate-pulse"></div>
                    ))}
                  </div>
                ) : blogPosts?.length > 0 ? (
                  <div className="space-y-4">
                    {blogPosts.map((post: any) => (
                      <Card key={post.id}>
                        <CardContent className="p-4">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center space-x-2 mb-2">
                                <h3 className="font-medium">{post.title}</h3>
                                <Badge 
                                  variant={post.isPublished ? "default" : "secondary"}
                                  className={post.isPublished ? "bg-success text-white" : ""}
                                >
                                  {post.isPublished ? 'প্রকাশিত' : 'খসড়া'}
                                </Badge>
                              </div>
                              {post.excerpt && (
                                <p className="text-sm text-muted-foreground mb-2">
                                  {post.excerpt}
                                </p>
                              )}
                              <div className="flex items-center text-xs text-muted-foreground">
                                <Calendar className="h-3 w-3 mr-1" />
                                <span>তৈরি: {getTimeAgo(post.createdAt)}</span>
                                {post.publishedAt && (
                                  <>
                                    <span className="mx-2">•</span>
                                    <span>প্রকাশ: {getTimeAgo(post.publishedAt)}</span>
                                  </>
                                )}
                              </div>
                            </div>
                            <div className="flex space-x-2">
                              <Button size="sm" variant="outline">
                                <Eye className="h-3 w-3 mr-1" />
                                দেখুন
                              </Button>
                              <Button size="sm" variant="outline">
                                <Edit className="h-3 w-3 mr-1" />
                                এডিট
                              </Button>
                              {!post.isPublished && (
                                <Button
                                  size="sm"
                                  onClick={() => publishBlogPostMutation.mutate(post.id)}
                                  disabled={publishBlogPostMutation.isPending}
                                  className="bg-success text-white hover:bg-success/90"
                                >
                                  <CheckCircle className="h-3 w-3 mr-1" />
                                  প্রকাশ করুন
                                </Button>
                              )}
                              <Button size="sm" variant="outline" className="text-destructive">
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <FileText className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-foreground mb-2">
                      কোন ব্লগ পোস্ট নেই
                    </h3>
                    <p className="text-muted-foreground mb-4">
                      আপনি এখনো কোন ব্লগ পোস্ট তৈরি করেননি
                    </p>
                    <Button 
                      onClick={() => setIsCreatePostOpen(true)}
                      className="bg-secondary text-secondary-foreground hover:bg-secondary/90"
                    >
                      প্রথম পোস্ট তৈরি করুন
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </main>

      <Footer />
    </div>
  );
}
