import { useState, useEffect, useRef } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { isUnauthorizedError } from "@/lib/authUtils";
import { useToast } from "@/hooks/use-toast";
import { api } from "@/lib/api";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { 
  X, 
  Send, 
  Paperclip, 
  Image as ImageIcon, 
  Phone, 
  Shield,
  Clock
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { bn } from "date-fns/locale";

interface ChatModalProps {
  isOpen: boolean;
  onClose: () => void;
  productId?: string | null;
}

export default function ChatModal({ isOpen, onClose, productId }: ChatModalProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [messageInput, setMessageInput] = useState("");
  const [selectedConversation, setSelectedConversation] = useState<string | null>(null);
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const { data: conversations, isLoading: conversationsLoading } = useQuery({
    queryKey: ['/api/conversations'],
    enabled: isOpen,
    refetchInterval: 10000, // Poll every 10 seconds for new conversations
  });

  const { data: product } = useQuery({
    queryKey: [`/api/products/${productId}`],
    enabled: !!productId && isOpen,
  });

  const { data: messages, isLoading: messagesLoading } = useQuery({
    queryKey: [`/api/conversations/${selectedConversation}/messages`],
    enabled: !!selectedConversation && isOpen,
    refetchInterval: 5000, // Poll every 5 seconds for new messages
  });

  const createConversationMutation = useMutation({
    mutationFn: ({ productId, sellerId }: { productId: string; sellerId: string }) =>
      api.createConversation(productId, sellerId),
    onSuccess: (conversation) => {
      setSelectedConversation(conversation.id);
      queryClient.invalidateQueries({ queryKey: ['/api/conversations'] });
    },
    onError: (error) => {
      if (isUnauthorizedError(error as Error)) {
        toast({
          title: "অনুমতি নেই",
          description: "আপনি লগআউট হয়ে গেছেন। আবার লগইন করুন...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "ত্রুটি",
        description: "কথোপকথন শুরু করতে পারিনি",
        variant: "destructive",
      });
    },
  });

  const sendMessageMutation = useMutation({
    mutationFn: ({ conversationId, content, image }: { 
      conversationId: string; 
      content?: string; 
      image?: File;
    }) => api.sendMessage(conversationId, { content, image }),
    onSuccess: () => {
      setMessageInput("");
      setSelectedImage(null);
      queryClient.invalidateQueries({ 
        queryKey: [`/api/conversations/${selectedConversation}/messages`] 
      });
      queryClient.invalidateQueries({ queryKey: ['/api/conversations'] });
    },
    onError: (error) => {
      if (isUnauthorizedError(error as Error)) {
        toast({
          title: "অনুমতি নেই",
          description: "আপনি লগআউট হয়ে গেছেন। আবার লগইন করুন...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/login";
        }, 500);
        return;
      }
      toast({
        title: "ত্রুটি",
        description: "মেসেজ পাঠাতে পারিনি",
        variant: "destructive",
      });
    },
  });

  useEffect(() => {
    if (isOpen && productId && product && user) {
      // Check if conversation already exists for this product
      const existingConversation = conversations?.find((conv: any) => 
        conv.productId === productId && 
        (conv.buyerId === user.id || conv.sellerId === user.id)
      );
      
      if (existingConversation) {
        setSelectedConversation(existingConversation.id);
      } else if (product.sellerId !== user.id) {
        // Create new conversation if user is not the seller
        createConversationMutation.mutate({
          productId,
          sellerId: product.sellerId,
        });
      }
    }
  }, [isOpen, productId, product, user, conversations]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  const getInitials = (firstName?: string, lastName?: string) => {
    return `${firstName?.[0] || ''}${lastName?.[0] || ''}`.toUpperCase() || 'U';
  };

  const getTimeAgo = (date: string) => {
    try {
      return formatDistanceToNow(new Date(date), { 
        addSuffix: true, 
        locale: bn 
      });
    } catch {
      return 'কিছুক্ষণ আগে';
    }
  };

  const isOnline = (lastSeen?: string) => {
    if (!lastSeen) return false;
    const lastSeenDate = new Date(lastSeen);
    const now = new Date();
    const diffInMinutes = (now.getTime() - lastSeenDate.getTime()) / (1000 * 60);
    return diffInMinutes < 5;
  };

  const handleSendMessage = () => {
    if (!selectedConversation) return;
    
    if (!messageInput.trim() && !selectedImage) {
      toast({
        title: "ত্রুটি",
        description: "মেসেজ লিখুন বা ছবি নির্বাচন করুন",
        variant: "destructive",
      });
      return;
    }

    sendMessageMutation.mutate({
      conversationId: selectedConversation,
      content: messageInput.trim() || undefined,
      image: selectedImage || undefined,
    });
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && file.type.startsWith('image/')) {
      setSelectedImage(file);
    } else {
      toast({
        title: "ত্রুটি",
        description: "শুধুমাত্র ছবি ফাইল নির্বাচন করুন",
        variant: "destructive",
      });
    }
  };

  const currentConversation = conversations?.find((conv: any) => conv.id === selectedConversation);
  const chatPartner = currentConversation ? 
    (currentConversation.buyerId === user?.id ? currentConversation.seller : currentConversation.buyer) :
    null;

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl h-[80vh] p-0">
        <div className="flex h-full">
          {/* Conversations List */}
          <div className="w-1/3 border-r border-border flex flex-col">
            <div className="p-4 border-b border-border">
              <h3 className="font-semibold">মেসেজ</h3>
            </div>
            
            <ScrollArea className="flex-1">
              {conversationsLoading ? (
                <div className="p-4 space-y-3">
                  {Array.from({ length: 3 }).map((_, i) => (
                    <div key={i} className="flex space-x-3 animate-pulse">
                      <div className="w-10 h-10 bg-muted rounded-full"></div>
                      <div className="flex-1 space-y-2">
                        <div className="bg-muted h-4 rounded w-3/4"></div>
                        <div className="bg-muted h-3 rounded w-1/2"></div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : conversations?.length > 0 ? (
                <div className="p-2">
                  {conversations.map((conversation: any) => {
                    const partner = conversation.buyerId === user?.id ? 
                      conversation.seller : conversation.buyer;
                    const lastMessage = conversation.messages?.[conversation.messages.length - 1];
                    
                    return (
                      <div
                        key={conversation.id}
                        className={`p-3 rounded-lg cursor-pointer hover:bg-muted transition-colors ${
                          selectedConversation === conversation.id ? 'bg-muted' : ''
                        }`}
                        onClick={() => setSelectedConversation(conversation.id)}
                      >
                        <div className="flex space-x-3">
                          <div className="relative">
                            <Avatar className="h-10 w-10">
                              <AvatarImage 
                                src={partner?.profileImageUrl || undefined}
                                className="object-cover"
                              />
                              <AvatarFallback className="bg-primary text-primary-foreground text-sm">
                                {getInitials(partner?.firstName, partner?.lastName)}
                              </AvatarFallback>
                            </Avatar>
                            <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-background ${
                              isOnline(partner?.lastSeen) ? 'bg-success' : 'bg-muted-foreground'
                            }`} />
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <h4 className="font-medium text-sm truncate">
                                {partner?.firstName} {partner?.lastName}
                              </h4>
                              {lastMessage && (
                                <span className="text-xs text-muted-foreground">
                                  {getTimeAgo(lastMessage.createdAt)}
                                </span>
                              )}
                            </div>
                            <p className="text-xs text-muted-foreground truncate">
                              {conversation.product?.title}
                            </p>
                            {lastMessage && (
                              <p className="text-xs text-muted-foreground truncate mt-1">
                                {lastMessage.messageType === 'image' ? '📷 ছবি' : lastMessage.content}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="p-4 text-center text-muted-foreground">
                  কোন কথোপকথন নেই
                </div>
              )}
            </ScrollArea>
          </div>

          {/* Chat Area */}
          <div className="w-2/3 flex flex-col">
            {selectedConversation && chatPartner ? (
              <>
                {/* Chat Header */}
                <div className="p-4 border-b border-border flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Avatar className="h-10 w-10">
                      <AvatarImage 
                        src={chatPartner.profileImageUrl || undefined}
                        className="object-cover"
                      />
                      <AvatarFallback className="bg-primary text-primary-foreground">
                        {getInitials(chatPartner.firstName, chatPartner.lastName)}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="flex items-center space-x-2">
                        <h3 className="font-medium">
                          {chatPartner.firstName} {chatPartner.lastName}
                        </h3>
                        {chatPartner.isVerified && (
                          <Shield className="h-4 w-4 text-success" />
                        )}
                      </div>
                      <div className="flex items-center text-sm text-muted-foreground">
                        <div className={`w-2 h-2 rounded-full mr-2 ${
                          isOnline(chatPartner.lastSeen) ? 'bg-success' : 'bg-muted-foreground'
                        }`} />
                        <span>
                          {isOnline(chatPartner.lastSeen) ? 'অনলাইন' : 'অফলাইন'}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm">
                      <Phone className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* Product Info */}
                {currentConversation?.product && (
                  <>
                    <div className="p-3 bg-muted/50 border-b border-border">
                      <div className="flex items-center space-x-3">
                        <img
                          src={currentConversation.product.images?.[0] || '/placeholder.jpg'}
                          alt={currentConversation.product.title}
                          className="w-12 h-12 object-cover rounded"
                        />
                        <div className="flex-1">
                          <h4 className="font-medium text-sm">
                            {currentConversation.product.title}
                          </h4>
                          <p className="text-sm font-semibold text-secondary">
                            ৳ {currentConversation.product.price}
                          </p>
                        </div>
                      </div>
                    </div>
                  </>
                )}

                {/* Messages */}
                <ScrollArea className="flex-1 p-4">
                  {messagesLoading ? (
                    <div className="space-y-4">
                      {Array.from({ length: 3 }).map((_, i) => (
                        <div key={i} className={`flex ${i % 2 === 0 ? 'justify-end' : 'justify-start'}`}>
                          <div className="bg-muted h-16 w-48 rounded-lg animate-pulse"></div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {messages?.map((message: any) => {
                        const isOwn = message.senderId === user?.id;
                        return (
                          <div key={message.id} className={`flex ${isOwn ? 'justify-end' : 'justify-start'}`}>
                            <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                              isOwn 
                                ? 'bg-primary text-primary-foreground' 
                                : 'bg-muted text-foreground'
                            }`}>
                              {message.messageType === 'image' && message.imageUrl ? (
                                <div className="mb-2">
                                  <img
                                    src={message.imageUrl}
                                    alt="Shared image"
                                    className="w-full h-32 object-cover rounded"
                                  />
                                </div>
                              ) : null}
                              {message.content && (
                                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                              )}
                              <div className="flex items-center justify-between mt-2">
                                <span className={`text-xs ${
                                  isOwn ? 'text-primary-foreground/70' : 'text-muted-foreground'
                                }`}>
                                  {getTimeAgo(message.createdAt)}
                                </span>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                      <div ref={messagesEndRef} />
                    </div>
                  )}
                </ScrollArea>

                {/* Message Input */}
                <div className="p-4 border-t border-border">
                  {selectedImage && (
                    <div className="mb-3 p-2 bg-muted rounded-lg flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <ImageIcon className="h-4 w-4" />
                        <span className="text-sm">{selectedImage.name}</span>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setSelectedImage(null)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                  
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => fileInputRef.current?.click()}
                    >
                      <ImageIcon className="h-4 w-4" />
                    </Button>
                    <Input
                      value={messageInput}
                      onChange={(e) => setMessageInput(e.target.value)}
                      placeholder="মেসেজ লিখুন..."
                      onKeyPress={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                          e.preventDefault();
                          handleSendMessage();
                        }
                      }}
                      className="flex-1"
                    />
                    <Button
                      onClick={handleSendMessage}
                      disabled={sendMessageMutation.isPending}
                      className="bg-primary text-primary-foreground hover:bg-primary/90"
                    >
                      <Send className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={handleFileSelect}
                  />
                </div>
              </>
            ) : (
              <div className="flex-1 flex items-center justify-center text-muted-foreground">
                <div className="text-center">
                  <div className="text-6xl mb-4">💬</div>
                  <p>একটি কথোপকথন নির্বাচন করুন</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
