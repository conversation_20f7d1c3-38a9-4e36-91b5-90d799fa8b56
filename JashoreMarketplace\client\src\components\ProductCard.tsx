import { useState } from "react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { Link, useLocation } from "wouter";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useToast } from "@/hooks/use-toast";
import { api } from "@/lib/api";
import { Heart, MessageSquare, MapPin, Shield, Clock } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { bn } from "date-fns/locale";
import { hideMobileNumbers } from "@/utils/textUtils";

interface ProductCardProps {
  product: any;
  viewMode: 'grid' | 'list';
  onChat: () => void;
}

export default function ProductCard({ product, viewMode, onChat }: ProductCardProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [, setLocation] = useLocation();
  const [imageError, setImageError] = useState(false);

  const { data: favoriteStatus } = useQuery({
    queryKey: [`/api/favorites/${product.id}/check`],
  });

  const toggleFavoriteMutation = useMutation({
    mutationFn: async () => {
      if (favoriteStatus?.isFavorite) {
        await api.removeFromFavorites(product.id);
      } else {
        await api.addToFavorites(product.id);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/favorites/${product.id}/check`] });
      queryClient.invalidateQueries({ queryKey: ['/api/favorites'] });
      toast({
        title: favoriteStatus?.isFavorite ? "পছন্দের তালিকা থেকে সরানো হয়েছে" : "পছন্দের তালিকায় যোগ করা হয়েছে",
        duration: 2000,
      });
    },
    onError: () => {
      toast({
        title: "ত্রুটি",
        description: "দয়া করে আবার চেষ্টা করুন",
        variant: "destructive",
      });
    },
  });

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('bn-BD', {
      style: 'currency',
      currency: 'BDT',
      minimumFractionDigits: 0,
    }).format(price).replace('BDT', '৳');
  };

  const getConditionBadge = (condition: string) => {
    if (condition === 'new') {
      return <Badge className="bg-success text-white">নতুন</Badge>;
    }
    return <Badge variant="secondary">ব্যবহৃত</Badge>;
  };

  const getTimeAgo = (date: string) => {
    try {
      return formatDistanceToNow(new Date(date), { 
        addSuffix: true, 
        locale: bn 
      });
    } catch {
      return 'কিছুক্ষণ আগে';
    }
  };

  const isOnline = (lastSeen?: string) => {
    if (!lastSeen) return false;
    const lastSeenDate = new Date(lastSeen);
    const now = new Date();
    const diffInMinutes = (now.getTime() - lastSeenDate.getTime()) / (1000 * 60);
    return diffInMinutes < 5;
  };

  const getInitials = (firstName?: string, lastName?: string) => {
    return `${firstName?.[0] || ''}${lastName?.[0] || ''}`.toUpperCase() || 'U';
  };

  const handleCardClick = (e: React.MouseEvent) => {
    // Don't navigate if clicking on buttons or interactive elements
    const target = e.target as HTMLElement;
    if (target.closest('button') || target.closest('a')) {
      return;
    }
    setLocation(`/product/${product.id}`);
  };

  const mainImage = product.images?.[0] && !imageError ? product.images[0] : null;

  if (viewMode === 'list') {
    return (
      <Card className="hover:shadow-md transition-shadow cursor-pointer" onClick={handleCardClick}>
        <CardContent className="p-4">
          <div className="flex space-x-4">
            <div className="relative w-32 h-24 flex-shrink-0">
              {mainImage ? (
                <img
                  src={mainImage}
                  alt={product.title}
                  className="w-full h-full object-cover rounded"
                  onError={() => setImageError(true)}
                />
              ) : (
                <div className="w-full h-full bg-muted rounded flex items-center justify-center">
                  <span className="text-muted-foreground text-sm">ছবি নেই</span>
                </div>
              )}
              <div className="absolute top-1 left-1">
                {getConditionBadge(product.condition)}
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="absolute top-1 right-1 h-6 w-6 p-0 bg-white/80 hover:bg-white"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  toggleFavoriteMutation.mutate();
                }}
              >
                <Heart 
                  className={`h-3 w-3 ${favoriteStatus?.isFavorite ? 'fill-red-500 text-red-500' : 'text-muted-foreground'}`}
                />
              </Button>
            </div>

            <div className="flex-1 min-w-0">
              <Link href={`/product/${product.id}`}>
                <h3 className="font-medium text-foreground mb-1 hover:text-primary cursor-pointer line-clamp-2">
                  {hideMobileNumbers(product.title)}
                </h3>
              </Link>
              <p className="text-lg font-semibold text-secondary mb-2">
                {formatPrice(parseFloat(product.price))}
              </p>
              
              <div className="flex items-center justify-between text-sm text-muted-foreground mb-3">
                <div className="flex items-center">
                  <MapPin className="h-3 w-3 mr-1" />
                  <span>{product.location?.areaBn}</span>
                </div>
                <div className="flex items-center">
                  <Clock className="h-3 w-3 mr-1" />
                  <span>{getTimeAgo(product.createdAt)}</span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Avatar className="h-6 w-6 mr-2">
                    <AvatarImage 
                      src={product.seller?.profileImageUrl || undefined}
                      className="object-cover"
                    />
                    <AvatarFallback className="bg-primary text-primary-foreground text-xs">
                      {getInitials(product.seller?.firstName, product.seller?.lastName)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="flex items-center">
                      <span className="text-xs text-foreground mr-1">
                        {product.seller?.firstName || 'নাম নেই'}
                      </span>
                      {product.seller?.isVerified && (
                        <Shield className="h-3 w-3 text-success" />
                      )}
                    </div>
                    <div className="flex items-center">
                      <div className={`w-2 h-2 rounded-full mr-1 ${
                        isOnline(product.seller?.lastSeen) ? 'bg-success' : 'bg-muted-foreground'
                      }`} />
                      <span className="text-xs text-muted-foreground">
                        {isOnline(product.seller?.lastSeen) ? 'অনলাইন' : 'অফলাইন'}
                      </span>
                    </div>
                  </div>
                </div>
                <Button
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    onChat();
                  }}
                  className="bg-primary text-primary-foreground hover:bg-primary/90 text-xs px-3 py-1"
                >
                  <MessageSquare className="h-3 w-3 mr-1" />
                  চ্যাট
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="hover:shadow-md transition-shadow cursor-pointer group" onClick={handleCardClick}>
      <div className="relative">
        {mainImage ? (
          <img
            src={mainImage}
            alt={product.title}
            className="w-full h-48 object-cover rounded-t-lg"
            onError={() => setImageError(true)}
          />
        ) : (
          <div className="w-full h-48 bg-muted rounded-t-lg flex items-center justify-center">
            <span className="text-muted-foreground">ছবি নেই</span>
          </div>
        )}
        <div className="absolute top-2 left-2">
          {getConditionBadge(product.condition)}
        </div>
        <Button
          variant="ghost"
          size="sm"
          className="absolute top-2 right-2 h-8 w-8 p-0 bg-white/80 hover:bg-white"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            toggleFavoriteMutation.mutate();
          }}
        >
          <Heart 
            className={`h-4 w-4 ${favoriteStatus?.isFavorite ? 'fill-red-500 text-red-500' : 'text-muted-foreground'}`}
          />
        </Button>
      </div>
      
      <CardContent className="p-4">
        <Link href={`/product/${product.id}`}>
          <h3 className="font-medium text-foreground mb-1 hover:text-primary line-clamp-2">
            {hideMobileNumbers(product.title)}
          </h3>
        </Link>
        <p className="text-lg font-semibold text-secondary mb-2">
          {formatPrice(parseFloat(product.price))}
        </p>
        
        <div className="flex items-center justify-between text-sm text-muted-foreground mb-3">
          <div className="flex items-center">
            <MapPin className="h-4 w-4 mr-1" />
            <span>{product.location?.areaBn}</span>
          </div>
          <span>{getTimeAgo(product.createdAt)}</span>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Avatar className="h-6 w-6 mr-2">
              <AvatarImage 
                src={product.seller?.profileImageUrl || undefined}
                className="object-cover"
              />
              <AvatarFallback className="bg-primary text-primary-foreground text-xs">
                {getInitials(product.seller?.firstName, product.seller?.lastName)}
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="flex items-center">
                <span className="text-xs text-foreground mr-1">
                  {product.seller?.firstName || 'নাম নেই'}
                </span>
                {product.seller?.isVerified && (
                  <Shield className="h-3 w-3 text-success" />
                )}
              </div>
              <div className="flex items-center">
                <div className={`w-2 h-2 rounded-full mr-1 ${
                  isOnline(product.seller?.lastSeen) ? 'bg-success pulse' : 'bg-muted-foreground'
                }`} />
                <span className="text-xs text-muted-foreground">
                  {isOnline(product.seller?.lastSeen) ? 'অনলাইন' : 'অফলাইন'}
                </span>
              </div>
            </div>
          </div>
          <Button
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              onChat();
            }}
            className="bg-primary text-primary-foreground hover:bg-primary/90 text-xs px-3 py-1"
          >
            <MessageSquare className="h-3 w-3 mr-1" />
            চ্যাট
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
