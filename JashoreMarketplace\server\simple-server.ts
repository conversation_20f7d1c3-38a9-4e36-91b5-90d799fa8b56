import dotenv from "dotenv";
dotenv.config();

console.log("Starting simple server...");

import express from "express";
console.log("Express imported");

const app = express();
console.log("Express app created");

app.use(express.json());
app.use(express.urlencoded({ extended: false }));

// Simple test route
app.get('/', (req, res) => {
  res.json({ message: 'Server is running!' });
});

app.get('/api/test', (req, res) => {
  res.json({ message: 'API is working!' });
});

const port = 5000;
app.listen(port, '0.0.0.0', () => {
  console.log(`Simple server running on port ${port}`);
});
