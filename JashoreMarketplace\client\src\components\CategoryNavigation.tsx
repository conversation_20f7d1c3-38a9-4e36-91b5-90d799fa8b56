import { useQuery } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Smartphone, 
  Car, 
  Home, 
  Laptop, 
  Shirt, 
  Briefcase, 
  GraduationCap, 
  MoreHorizontal,
  Gamepad2,
  Baby,
  Utensils,
  Heart
} from "lucide-react";

interface CategoryNavigationProps {
  onCategorySelect: (categoryId?: number) => void;
}

const categoryIcons: Record<string, any> = {
  mobile: Smartphone,
  car: Car,
  property: Home,
  electronics: Laptop,
  fashion: Shirt,
  jobs: Briefcase,
  education: GraduationCap,
  gaming: Gamepad2,
  baby: Baby,
  food: Utensils,
  health: Heart,
  other: MoreHorizontal,
};

export default function CategoryNavigation({ onCategorySelect }: CategoryNavigationProps) {
  const { data: categories } = useQuery({
    queryKey: ['/api/categories'],
  });

  const getIcon = (slug: string) => {
    const IconComponent = categoryIcons[slug] || categoryIcons.other;
    return <IconComponent className="h-5 w-5" />;
  };

  return (
    <nav className="bg-card border-b border-border">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <ScrollArea className="w-full">
          <div className="flex space-x-8 py-3 min-w-max">
            <Button
              variant="ghost"
              className="flex flex-col items-center min-w-max text-muted-foreground hover:text-primary transition-colors h-auto py-2 px-3"
              onClick={() => onCategorySelect(undefined)}
            >
              <MoreHorizontal className="h-5 w-5 mb-1" />
              <span className="text-xs">সব বিভাগ</span>
            </Button>
            
            {categories?.map((category: any) => (
              <Button
                key={category.id}
                variant="ghost"
                className="flex flex-col items-center min-w-max text-muted-foreground hover:text-primary transition-colors h-auto py-2 px-3"
                onClick={() => onCategorySelect(category.id)}
              >
                {getIcon(category.slug)}
                <span className="text-xs mt-1">{category.nameBn}</span>
              </Button>
            ))}
          </div>
        </ScrollArea>
      </div>
    </nav>
  );
}
