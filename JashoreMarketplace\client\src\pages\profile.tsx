import { useState, useEffect } from "react";
import { useAuth } from "@/hooks/useAuth";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { isUnauthorizedError } from "@/lib/authUtils";
import { useToast } from "@/hooks/use-toast";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import ProductCard from "@/components/ProductCard";
import UserProductCard from "@/components/UserProductCard";
import EditProductModal from "@/components/EditProductModal";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  User, 
  MapPin, 
  Calendar, 
  Shield, 
  Store, 
  Heart, 
  Phone, 
  Mail,
  Edit,
  Save,
  X
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { bn } from "date-fns/locale";

export default function Profile() {
  const { user, isAuthenticated, isLoading } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isEditing, setIsEditing] = useState(false);
  const [editingProduct, setEditingProduct] = useState(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editForm, setEditForm] = useState({
    firstName: '',
    lastName: '',
    phone: '',
  });

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      toast({
        title: "অনুমতি নেই",
        description: "আপনি লগআউট হয়ে গেছেন। আবার লগইন করুন...",
        variant: "destructive",
      });
      setTimeout(() => {
        window.location.href = "/api/login";
      }, 500);
      return;
    }

    if (user) {
      setEditForm({
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        phone: user.phone || '',
      });
    }
  }, [isAuthenticated, isLoading, user, toast]);

  const { data: userProducts, isLoading: productsLoading } = useQuery({
    queryKey: [`/api/users/${user?.id}/products`],
    enabled: !!user?.id,
  });

  const { data: favorites, isLoading: favoritesLoading } = useQuery({
    queryKey: ['/api/favorites'],
    enabled: isAuthenticated,
  });

  const updateProfileMutation = useMutation({
    mutationFn: async (data: typeof editForm) => {
      // This would need to be implemented in the backend
      const response = await fetch('/api/auth/user', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      if (!response.ok) throw new Error('Failed to update profile');
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/auth/user'] });
      setIsEditing(false);
      toast({
        title: "প্রোফাইল আপডেট করা হয়েছে",
        duration: 2000,
      });
    },
    onError: (error) => {
      if (isUnauthorizedError(error as Error)) {
        toast({
          title: "অনুমতি নেই",
          description: "আপনি লগআউট হয়ে গেছেন। আবার লগইন করুন...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/login";
        }, 500);
        return;
      }
      toast({
        title: "ত্রুটি",
        description: "প্রোফাইল আপডেট করতে পারিনি",
        variant: "destructive",
      });
    },
  });

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <Header 
          onOpenChat={() => {}}
          onOpenPostModal={() => {}}
          onSearch={() => {}}
          onLocationChange={() => {}}
        />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="animate-pulse space-y-6">
            <div className="bg-muted h-48 rounded-lg"></div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-muted h-64 rounded-lg"></div>
              <div className="md:col-span-2 bg-muted h-64 rounded-lg"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!isAuthenticated || !user) {
    return null;
  }

  const getInitials = (firstName?: string, lastName?: string) => {
    return `${firstName?.[0] || ''}${lastName?.[0] || ''}`.toUpperCase() || 'U';
  };

  const getJoinDate = (date?: string) => {
    if (!date) return 'তারিখ নেই';
    try {
      return formatDistanceToNow(new Date(date), { 
        addSuffix: true, 
        locale: bn 
      });
    } catch {
      return 'কিছুক্ষণ আগে';
    }
  };

  const isOnline = (lastSeen?: string) => {
    if (!lastSeen) return false;
    const lastSeenDate = new Date(lastSeen);
    const now = new Date();
    const diffInMinutes = (now.getTime() - lastSeenDate.getTime()) / (1000 * 60);
    return diffInMinutes < 5;
  };

  const handleSaveProfile = () => {
    updateProfileMutation.mutate(editForm);
  };

  const handleCancelEdit = () => {
    setEditForm({
      firstName: user.firstName || '',
      lastName: user.lastName || '',
      phone: user.phone || '',
    });
    setIsEditing(false);
  };

  const handleEditProduct = (product: any) => {
    setEditingProduct(product);
    setIsEditModalOpen(true);
  };

  const handleCloseEditModal = () => {
    setEditingProduct(null);
    setIsEditModalOpen(false);
  };

  return (
    <div className="min-h-screen bg-background">
      <Header 
        onOpenChat={() => {}}
        onOpenPostModal={() => {}}
        onSearch={() => {}}
        onLocationChange={() => {}}
      />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Profile Header */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-6">
              <div className="relative">
                <Avatar className="h-24 w-24">
                  <AvatarImage 
                    src={user.profileImageUrl || undefined}
                    className="object-cover"
                  />
                  <AvatarFallback className="bg-primary text-primary-foreground text-2xl">
                    {getInitials(user.firstName, user.lastName)}
                  </AvatarFallback>
                </Avatar>
                <div className={`absolute -bottom-1 -right-1 w-6 h-6 rounded-full border-4 border-background ${
                  isOnline(user.lastSeen) ? 'bg-success' : 'bg-muted-foreground'
                }`} />
              </div>

              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-2">
                  <h1 className="text-2xl font-bold">
                    {user.firstName} {user.lastName}
                  </h1>
                  {user.isVerified && (
                    <Badge className="bg-success text-white">
                      <Shield className="h-3 w-3 mr-1" />
                      যাচাইকৃত
                    </Badge>
                  )}
                  {user.isAdmin && (
                    <Badge variant="secondary">
                      অ্যাডমিন
                    </Badge>
                  )}
                </div>

                <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground mb-3">
                  {user.email && (
                    <div className="flex items-center">
                      <Mail className="h-4 w-4 mr-1" />
                      <span>{user.email}</span>
                    </div>
                  )}
                  {user.phone && (
                    <div className="flex items-center">
                      <Phone className="h-4 w-4 mr-1" />
                      <span>{user.phone}</span>
                    </div>
                  )}
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-1" />
                    <span>যোগদান {getJoinDate(user.createdAt)}</span>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <div className={`w-2 h-2 rounded-full ${
                    isOnline(user.lastSeen) ? 'bg-success pulse' : 'bg-muted-foreground'
                  }`} />
                  <span className="text-sm text-muted-foreground">
                    {isOnline(user.lastSeen) ? 'অনলাইন' : 'অফলাইন'}
                  </span>
                </div>
              </div>

              <Button
                variant="outline"
                onClick={() => setIsEditing(!isEditing)}
                disabled={updateProfileMutation.isPending}
              >
                <Edit className="h-4 w-4 mr-2" />
                প্রোফাইল এডিট করুন
              </Button>
            </div>

            {/* Edit Form */}
            {isEditing && (
              <>
                <Separator className="my-6" />
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="firstName">নামের প্রথম অংশ</Label>
                    <Input
                      id="firstName"
                      value={editForm.firstName}
                      onChange={(e) => setEditForm(prev => ({ ...prev, firstName: e.target.value }))}
                      placeholder="আপনার নামের প্রথম অংশ"
                    />
                  </div>
                  <div>
                    <Label htmlFor="lastName">নামের শেষ অংশ</Label>
                    <Input
                      id="lastName"
                      value={editForm.lastName}
                      onChange={(e) => setEditForm(prev => ({ ...prev, lastName: e.target.value }))}
                      placeholder="আপনার নামের শেষ অংশ"
                    />
                  </div>
                  <div>
                    <Label htmlFor="phone">মোবাইল নম্বর</Label>
                    <Input
                      id="phone"
                      value={editForm.phone}
                      onChange={(e) => setEditForm(prev => ({ ...prev, phone: e.target.value }))}
                      placeholder="আপনার মোবাইল নম্বর"
                    />
                  </div>
                </div>
                <div className="flex space-x-2 mt-4">
                  <Button
                    onClick={handleSaveProfile}
                    disabled={updateProfileMutation.isPending}
                    className="bg-primary text-primary-foreground hover:bg-primary/90"
                  >
                    <Save className="h-4 w-4 mr-2" />
                    {updateProfileMutation.isPending ? 'সেভ করা হচ্ছে...' : 'সেভ করুন'}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={handleCancelEdit}
                    disabled={updateProfileMutation.isPending}
                  >
                    <X className="h-4 w-4 mr-2" />
                    বাতিল
                  </Button>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Profile Content */}
        <Tabs defaultValue="products" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="products" className="flex items-center">
              <Store className="h-4 w-4 mr-2" />
              আমার বিজ্ঞাপন ({userProducts?.total || 0})
            </TabsTrigger>
            <TabsTrigger value="favorites" className="flex items-center">
              <Heart className="h-4 w-4 mr-2" />
              পছন্দের তালিকা ({favorites?.length || 0})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="products">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Store className="h-5 w-5 mr-2" />
                  আমার বিজ্ঞাপন
                </CardTitle>
              </CardHeader>
              <CardContent>
                {productsLoading ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {Array.from({ length: 6 }).map((_, i) => (
                      <div key={i} className="bg-muted rounded-lg p-4 animate-pulse">
                        <div className="bg-card h-48 rounded mb-4"></div>
                        <div className="bg-card h-4 rounded mb-2"></div>
                        <div className="bg-card h-4 rounded w-2/3"></div>
                      </div>
                    ))}
                  </div>
                ) : userProducts?.products?.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {userProducts.products.map((product: any) => (
                      <UserProductCard
                        key={product.id}
                        product={product}
                        viewMode="grid"
                        onEdit={handleEditProduct}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <Store className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-foreground mb-2">
                      কোন বিজ্ঞাপন নেই
                    </h3>
                    <p className="text-muted-foreground mb-4">
                      আপনি এখনো কোন বিজ্ঞাপন দেননি
                    </p>
                    <Button className="bg-secondary text-secondary-foreground hover:bg-secondary/90">
                      প্রথম বিজ্ঞাপন দিন
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="favorites">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Heart className="h-5 w-5 mr-2" />
                  পছন্দের তালিকা
                </CardTitle>
              </CardHeader>
              <CardContent>
                {favoritesLoading ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {Array.from({ length: 6 }).map((_, i) => (
                      <div key={i} className="bg-muted rounded-lg p-4 animate-pulse">
                        <div className="bg-card h-48 rounded mb-4"></div>
                        <div className="bg-card h-4 rounded mb-2"></div>
                        <div className="bg-card h-4 rounded w-2/3"></div>
                      </div>
                    ))}
                  </div>
                ) : favorites?.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {favorites.map((product: any) => (
                      <ProductCard
                        key={product.id}
                        product={product}
                        viewMode="grid"
                        onChat={() => {}}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <Heart className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-foreground mb-2">
                      পছন্দের তালিকা খালি
                    </h3>
                    <p className="text-muted-foreground">
                      আপনার পছন্দের পণ্যগুলি এখানে দেখা যাবে
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </main>

      {/* Edit Product Modal */}
      <EditProductModal
        isOpen={isEditModalOpen}
        onClose={handleCloseEditModal}
        product={editingProduct}
      />

      <Footer />
    </div>
  );
}
